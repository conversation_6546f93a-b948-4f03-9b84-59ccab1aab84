# Production PostgreSQL configuration for optimal performance
# These settings are designed for a standard production instance
# Adjust values based on your actual server specifications

# Memory settings - adjust based on available server RAM
# For a server with 4GB RAM
shared_buffers = 1GB                  # 25% of RAM
work_mem = 32MB                       # Higher for complex queries
maintenance_work_mem = 256MB          # For maintenance operations
effective_cache_size = 2GB            # ~50% of RAM

# Parallelism settings - adjust based on available CPUs
max_worker_processes = 8              # Number of CPUs
max_parallel_workers_per_gather = 4   # Half of CPUs
max_parallel_workers = 8              # Number of CPUs

# Write settings
wal_buffers = 16MB                    # Related to shared_buffers
checkpoint_completion_target = 0.9    # Spread out checkpoint I/O
synchronous_commit = off              # Improves performance at slight risk of data loss

# Query planning
random_page_cost = 1.1                # Lower for SSDs
effective_io_concurrency = 200        # Higher for SSDs
default_statistics_target = 100       # Better query plans

# Connection settings
max_connections = 100                 # Adjust based on expected load

# Logging for monitoring (enable temporarily when needed)
log_min_duration_statement = 1000     # Log queries taking more than 1 second
log_checkpoints = on                  # Log checkpoint information
log_lock_waits = on                   # Log lock waits
log_temp_files = 0                    # Log all temp files

# Autovacuum settings
autovacuum = on
autovacuum_max_workers = 3
autovacuum_naptime = 1min
autovacuum_vacuum_threshold = 50
autovacuum_analyze_threshold = 50
