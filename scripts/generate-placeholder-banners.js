const fs = require('fs');
const path = require('path');
const { createCanvas } = require('canvas');

// Ensure the examples directory exists
const examplesDir = path.join(__dirname, '../public/images/examples');
if (!fs.existsSync(examplesDir)) {
  fs.mkdirSync(examplesDir, { recursive: true });
}

// Banner sizes and formats to generate
const bannerFormats = [
  { name: 'banner-example-1', width: 300, height: 250, color: '#4285F4', text: 'Summer Sale' },
  { name: 'banner-example-2', width: 728, height: 90, color: '#34A853', text: 'Travel Deals' },
  { name: 'banner-example-3', width: 300, height: 600, color: '#FBBC05', text: 'App Download' },
  { name: 'banner-example-4', width: 336, height: 280, color: '#EA4335', text: 'New Product' },
  { name: 'banner-example-5', width: 300, height: 250, color: '#8E44AD', text: 'Event Promo' },
  { name: 'banner-example-6', width: 970, height: 250, color: '#16A085', text: 'Subscribe Now' },
];

// Generate placeholder banners
bannerFormats.forEach(format => {
  const canvas = createCanvas(format.width, format.height);
  const ctx = canvas.getContext('2d');

  // Fill background
  ctx.fillStyle = format.color;
  ctx.fillRect(0, 0, format.width, format.height);

  // Add text
  ctx.fillStyle = '#FFFFFF';
  ctx.font = `bold ${Math.min(format.width, format.height) / 10}px Arial`;
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';
  ctx.fillText(format.text, format.width / 2, format.height / 2);

  // Add size text
  ctx.font = `${Math.min(format.width, format.height) / 20}px Arial`;
  ctx.fillText(`${format.width}x${format.height}`, format.width / 2, format.height / 2 + Math.min(format.width, format.height) / 8);

  // Save the image
  const buffer = canvas.toBuffer('image/png');
  fs.writeFileSync(path.join(examplesDir, `${format.name}.png`), buffer);
  console.log(`Generated ${format.name}.png (${format.width}x${format.height})`);
});

console.log('All placeholder banners generated successfully!');
