# Lemon Squeezy Webhook Configuration Guide

This guide will help you configure Lemon Squeezy webhooks to properly update your application when subscription events occur.

## Step 1: Generate a Webhook Secret

First, you need to generate a secure webhook secret. You can use the following command to generate a random string:

```bash
openssl rand -hex 32
```

Copy the generated string and add it to your `.env.local` file:

```
LEMON_SQUEEZY_WEBHOOK_SECRET=your_generated_secret
```

## Step 2: Log in to your Lemon Squeezy account

Go to [app.lemonsqueezy.com](https://app.lemonsqueezy.com) and log in to your account.

## Step 3: Navigate to the Webhooks section

Click on "Settings" in the left sidebar, then select "Webhooks" from the dropdown menu.

## Step 4: Create a new webhook

Click the "Add Webhook" button to create a new webhook.

## Step 5: Configure the webhook

Fill in the following information:

1. **URL**: Enter your webhook URL. For local development, you'll need to use a service like ngrok to expose your local server to the internet. For production, use your production URL:
   ```
   https://your-domain.com/api/webhooks/lemon-squeezy
   ```

2. **Events**: Select the following events:
   - Order Created
   - Subscription Created
   - Subscription Updated
   - Subscription Cancelled

3. **Secret**: Enter the webhook secret you generated in Step 1.

## Step 6: Save the webhook

Click the "Save Webhook" button to create the webhook.

## Step 7: Test the webhook

You can test the webhook by clicking the "Test" button next to the webhook in the Lemon Squeezy dashboard. This will send a test event to your webhook URL.

## Step 8: Update your environment variables

Make sure your production environment has the `LEMON_SQUEEZY_WEBHOOK_SECRET` environment variable set to the same value you used in Step 1.

## Why webhooks are important

Webhooks are essential for ensuring that your application stays in sync with Lemon Squeezy. They provide a reliable way to receive notifications about subscription events, even if the user doesn't return to your application after completing the checkout process.

Some key benefits of using webhooks:

1. **Reliability**: Webhooks ensure that your application receives notifications about subscription events, even if the user closes their browser after completing the checkout.

2. **Real-time updates**: Webhooks allow your application to update the user's subscription status in real-time, without requiring the user to take any additional actions.

3. **Security**: Webhooks include a signature that allows you to verify that the request came from Lemon Squeezy, preventing malicious actors from spoofing subscription events.

## Testing webhooks locally

To test webhooks locally, you'll need to use a service like ngrok to expose your local server to the internet. Here's how:

1. Install ngrok: [https://ngrok.com/download](https://ngrok.com/download)

2. Start your Next.js development server:
   ```bash
   npm run dev
   ```

3. In a separate terminal, start ngrok:
   ```bash
   ngrok http 3000
   ```

4. Copy the ngrok URL (e.g., `https://1234abcd.ngrok.io`) and use it as the base URL for your webhook in the Lemon Squeezy dashboard:
   ```
   https://1234abcd.ngrok.io/api/webhooks/lemon-squeezy
   ```

5. Test the webhook by creating a test subscription in Lemon Squeezy.

## Troubleshooting

If you're having issues with webhooks, check the following:

1. **Webhook URL**: Make sure the webhook URL is correct and accessible from the internet.

2. **Webhook Secret**: Make sure the webhook secret in your `.env.local` file matches the one in the Lemon Squeezy dashboard.

3. **Event Selection**: Make sure you've selected all the necessary events in the Lemon Squeezy dashboard.

4. **Server Logs**: Check your server logs for any errors related to webhook processing.

If you're still having issues, contact Lemon Squeezy support for assistance.
