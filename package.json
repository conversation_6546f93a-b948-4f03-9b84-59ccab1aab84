{"name": "banner-so", "version": "1.0.0", "description": "A SAAS application for creating HTML5 animated banners using Remotion", "scripts": {"dev": "npx rimraf .next && next dev", "dev:turbo": "npx rimraf .next && next dev --turbopack", "dev:https": "npx rimraf .next && node server.js", "build": "next build", "start": "next start", "list": "node scripts/dev.js list", "generate": "node scripts/dev.js generate", "parse-prd": "node scripts/dev.js parse-prd", "convert-to-ts": "node scripts/convert-to-typescript.js", "type-check": "tsc --noEmit"}, "dependencies": {"@ai-sdk/openai": "^1.3.22", "@ai-sdk/react": "^1.2.12", "@aws-sdk/client-s3": "^3.726.1", "@paralleldrive/cuid2": "^2.2.2", "@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-checkbox": "^1.3.0", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toast": "^1.2.11", "@remotion/gif": "^4.0.310", "@remotion/player": "^4.0.295", "@remotion/renderer": "^4.0.310", "@remotion/webcodecs": "^4.0.310", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "ai": "^4.3.15", "canvas": "^3.1.0", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "formidable": "^3.5.2", "framer-motion": "^12.9.4", "html-to-image": "^1.11.13", "html2canvas": "^1.4.1", "image-size": "^1.2.0", "lucide-react": "^0.506.0", "next": "^15.2.3", "next-recaptcha-v3": "^1.5.2", "openai": "^4.24.1", "posthog-js": "^1.234.9", "qrcode.react": "^4.2.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.3.8", "react-icons": "^5.5.0", "react-select": "^5.9.0", "recharts": "^2.15.1", "remotion": "^4.0.280", "sharp": "^0.33.5", "slugify": "^1.6.6", "swr": "^2.2.5", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.4"}, "devDependencies": {"@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-navigation-menu": "^1.2.10", "@tailwindcss/forms": "^0.5.7", "@types/node": "^22.15.3", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.3", "autoprefixer": "^10.4.18", "postcss": "^8.4.35", "posthog-node": "^4.16.0", "tailwindcss": "^3.4.1", "typescript": "^5.8.3"}}