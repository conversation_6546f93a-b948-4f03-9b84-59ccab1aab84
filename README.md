# Banner.so - AI-Powered HTML5 Animated Banner Creator

Banner.so is a modern Software-as-a-Service (SaaS) platform built with Next.js 15 (App Router) and Supabase, designed to help marketers, designers, and agencies create stunning, high-performing HTML5 animated ad banners with ease. It leverages AI capabilities and provides an intuitive visual editor, aiming to be a streamlined alternative to existing banner creation tools.


## ✨ Features

* **User Authentication:** Secure user registration, login, password reset, and profile management powered by Supabase Auth.
* **Project Management:** Organize banners into distinct projects with titles, descriptions, and status (active/archived).
* **Intuitive Banner Editor:**
    * Visual drag-and-drop canvas.
    * Support for various elements: Text, Images, Buttons, Shapes, Ratings, QR Codes.
    * Detailed style customization for elements (position, size, color, fonts, borders, shadows, etc.).
    * Background options: Solid color and linear gradients.
    * Layer management for element stacking order.
* **Animation Timeline:** Frame-based timeline for creating and managing element entrance, exit, and potentially other animations. Control start/end times and duration.
* **Format Management:** Support for standard IAB ad sizes and creation/management of custom banner dimensions.
* **Asset Management:** Upload, store (using Supabase Storage or local fallback), and manage images for use in banners, associated with specific projects or globally.
* **AI Studio (Admin):** Generate banner designs based on prompts using OpenAI (GPT-4 Turbo for design structure, gpt-image-1 for creative images). Includes generation history.
* **Template System (Admin):**
    * Create, edit, and manage banner templates.
    * Organize templates using Categories, Subcategories, and Tags.
    * Publish/unpublish templates.
    * Generate thumbnails for templates.
* **Export & Publish:**
    * Export banners as self-contained HTML5 files.
    * Publish banners to generate shareable URLs (hosted within the app).
    * (Planned/Example) Integration points for publishing to ad networks (Google Ads, Meta, etc.).
* **Subscription Management (Integration with Lemon Squeezy):**
    * API routes for handling checkout generation, plan changes, cancellations, and status checks via Lemon Squeezy.
    * Webhook endpoint to receive and process subscription updates from Lemon Squeezy.
* **Admin Panel:** Dedicated section for managing users, roles (placeholder), templates, categories, subcategories, tags, and viewing waitlist entries.
* **Stock Image/Icon Integration:** Search and use stock images (Unsplash, Pexels) and icons (Iconfinder) directly within the editor.

## 🚀 Tech Stack

* **Framework:** Next.js 15 (App Router)
* **Language:** JavaScript (React)
* **Styling:** Tailwind CSS with shadcn/ui components
* **Backend & Database:** Supabase (PostgreSQL, Auth, Storage)
* **Animation (Preview/Export):** Remotion (likely used for video export/complex previews), CSS Animations (for HTML5 export)
* **State Management:** React Context (AuthContext), `useState`, `useReducer` (AIStudioReducer), `useSWR` (for data fetching)
* **AI:** OpenAI API (GPT-4 Turbo, gpt-image-1)
* **Payments:** Lemon Squeezy (via API and Webhooks)
* **Image/Icon APIs:** Unsplash, Pexels, Iconfinder
* **Utilities:** `react-select`, `react-dropzone`, `html-to-image`, `qrcode.react`, `slugify`, `canvas-confetti`

## 📂 Project Structure

.├── public/             # Static assets (images, fonts, published banners)├── src/│   ├── app/            # Next.js App Router core│   │   ├── (auth)/     # Authentication pages (login, register, etc.)│   │   ├── admin/      # Admin panel routes│   │   ├── api/        # API route handlers│   │   ├── dashboard/  # Main user dashboard│   │   ├── editor/     # Banner editor page│   │   ├── subscription/ # Subscription management pages│   │   ├── contexts/   # React Context providers (e.g., AuthContext)│   │   ├── layout.js   # Root layout│   │   └── page.jsx    # Landing page│   ├── components/     # Reusable React components│   │   ├── common/     # General UI components (Button, Modal)│   │   ├── dashboard/  # Dashboard specific components│   │   ├── editor/     # Editor specific components (sidebars, canvas wrapper)│   │   ├── layout/     # Layout components (Navbar, Footer)│   │   ├── sections/   # Landing page sections│   │   ├── templates/  # Template preview components│   │   ├── timeline/   # Timeline editor components│   │   └── ui/         # shadcn/ui components│   ├── constants/      # Shared constants (formats, styles, animations)│   ├── hooks/          # Custom React hooks│   ├── lib/            # Core libraries, Supabase clients, external services│   ├── middleware/     # Next.js middleware (authentication)│   ├── reducers/       # State reducers (e.g., AI Studio)│   ├── services/       # Business logic services (e.g., AI Studio)│   ├── styles/         # Global CSS and fonts│   ├── types/          # Type definitions (if using TypeScript or JSDoc)│   └── utils/          # Utility functions (API helpers, date, editor handlers)├── supabase/           # Supabase local development config, migrations, seed├── .env.example        # Environment variable template├── next.config.js      # Next.js configuration├── package.json        # Project dependencies and scripts└── README.md           # This file
## ⚙️ Getting Started

Follow these steps to set up and run the project locally.

### Prerequisites

* Node.js (v18.17 or later recommended)
* npm or yarn
* Supabase Account (for cloud deployment) or Supabase CLI / Docker (for local development)
* API Keys for external services (OpenAI, Unsplash, Pexels, Iconfinder, Lemon Squeezy)

### Installation & Setup

1.  **Clone the repository:**
    ```bash
    git clone <your-repository-url>
    cd banner-so-saas
    ```

2.  **Install dependencies:**
    ```bash
    npm install
    # or
    yarn install
    ```

3.  **Set up Environment Variables:**
    * Copy the example environment file:
        ```bash
        cp .env.example .env.local
        ```
    * Fill in the required values in `.env.local`:
        * **Supabase Credentials:** Get these from your Supabase project settings (`NEXT_PUBLIC_SUPABASE_URL`, `NEXT_PUBLIC_SUPABASE_ANON_KEY`). For local development, use the values provided by `supabase start`.
        * **Supabase Service Role Key:** (`SUPABASE_SERVICE_ROLE_KEY`) Found in your Supabase project's API settings (use with extreme caution, **never expose publicly**). Needed for admin actions and potentially seed scripts.
        * **External API Keys:** Add keys for OpenAI, Unsplash, Pexels, Iconfinder, Lemon Squeezy.
        * **JWT Secret:** A strong, random secret for session management (`NEXTAUTH_SECRET` or a custom JWT secret if not using NextAuth).
        * **App URL:** (`NEXT_PUBLIC_APP_URL`) The base URL where your app runs locally (e.g., `http://localhost:3000`).
        * **Storage Provider:** Set `STORAGE_PROVIDER` to `s3` (for Supabase Storage) or `local`. If using `s3`, configure `NEXT_PUBLIC_S3_BUCKET_NAME` and `NEXT_PUBLIC_S3_REGION`.

4.  **Set up Supabase Locally (Recommended for Development):**
    * Ensure you have the Supabase CLI installed (`npm install -g supabase`) and Docker running.
    * Navigate to the `supabase` directory: `cd supabase`
    * Start the Supabase services:
        ```bash
        supabase start
        ```
    * Note the local Supabase URL, anon key, and service role key provided in the output and update your `.env.local` file accordingly.
    * Apply database migrations:
        ```bash
        supabase db push
        ```
    * (Optional) Run the seed script if needed (check `supabase/README.md` for instructions if a seed script exists):
        ```bash
        # Example: node seed.js (if using the node script)
        # Or potentially: supabase db reset (if using SQL seed)
        ```
    * Navigate back to the project root: `cd ..`

5.  **Start the Development Server:**
    ```bash
    npm run dev
    # or
    yarn dev
    ```

6.  Open your browser and navigate to `http://localhost:3000` (or your configured port).

## 🔑 Environment Variables

Ensure the following variables are set in your `.env.local` file:

* `NEXT_PUBLIC_SUPABASE_URL`: Your Supabase project URL.
* `NEXT_PUBLIC_SUPABASE_ANON_KEY`: Your Supabase project anon key.
* `SUPABASE_SERVICE_ROLE_KEY`: Your Supabase service role key (for server-side admin actions).
* `NEXT_PUBLIC_APP_URL`: The base URL of your application (e.g., `http://localhost:3000`).
* `OPENAI_API_KEY`: For AI Studio features.
* `LEMON_SQUEEZY_API_KEY`: For subscription management API calls.
* `LEMON_SQUEEZY_STORE_ID`: Your Lemon Squeezy store ID.
* `LEMON_SQUEEZY_WEBHOOK_SECRET`: Secret for verifying Lemon Squeezy webhooks.
* `NEXT_PUBLIC_LEMON_SQUEEZY_..._VARIANT_ID`: Product/Variant IDs for different subscription tiers.
* `STORAGE_PROVIDER`: `s3` or `local`.
* *(Optional)* `UNSPLASH_ACCESS_KEY`, `PEXELS_API_KEY`, `ICONFINDER_API_KEY`.
* *(Optional)* SMTP credentials if configuring email sending.

## 💡 Key Concepts

* **App Router:** Uses Next.js 15's App Router for file-based routing and Server/Client Components.
* **Supabase Integration:** Leverages Supabase for Authentication, PostgreSQL Database (with RLS potentially), and Storage. Client and Server components use appropriate Supabase clients (`@supabase/ssr`).
* **Editor State:** Managed primarily through React state (`useState`) within the `src/app/editor/page.js` component, along with custom hooks (`useBannerData`, `useHistory`) and potentially reducers (`aiStudioReducer`).
* **Component Structure:** Follows a feature-based organization within `src/components`. `shadcn/ui` is used for base UI components.
* **API Routes:** Backend logic is handled via API Route Handlers in `src/app/api/`.
* **Middleware:** `src/middleware.js` handles authentication checks and redirects for protected routes.

## 🤝 Contributing

*(Optional: Add guidelines here if you plan to accept contributions)*

## 📄 License

This project is licensed under the MIT License. See the `LICENSE` file (if one exists) for details.
