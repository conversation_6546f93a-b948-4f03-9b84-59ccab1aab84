
AI UI/UX Prompt for HTML5 Banner SaaS App (Next.js v15, Remotion)

=======================
Overview of the Product
=======================

We are building a modern Next.js v15 SaaS app to help marketers and creatives design AI-powered animated HTML5 ad banners. It will compete with platforms like Bannerflow, Abyssale, and Creatopy, but with a streamlined, developer-first edge.

The application already has the core canvas system implemented using Remotion and CSS animations.

=======================
Competitor Feature Summary
=======================

**Bannerflow:**
- Drag-and-drop banner builder with animation timeline
- AI image editing (remove/replace/refine/expand)
- Smart scaling and auto-formatting
- Live data feeds for dynamic content
- Collaboration tools (live preview, commenting)
- Direct publishing to ad networks
- Export to multiple formats (HTML5, MP4, etc.)

**Abyssale:**
- Drag-and-drop design system
- AI tools: background removal, translation, text-to-image
- Spreadsheet-based bulk generation
- Dynamic content via API and integrations (Zapier, Airtable)
- Team collaboration with approval workflows
- Brand governance and access management

**Creatopy:**
- AI ad generation from brand assets and URLs
- Responsive design resizing
- Live updates through Creatopy-hosted tags
- Central brand kit for fonts/colors/logos
- Batch editing, automation, A/B testing

=======================
Prompt to AI Engineer
=======================

We're building a modern Next.js v15 SaaS app to help marketers and creatives design AI-powered animated HTML5 ad banners. It will compete with platforms like Bannerflow, Abyssale, and Creatopy, but with a streamlined, developer-first edge.

We've already implemented a core canvas system using Remotion and CSS animations. Now we need a complete UI/UX design that supports real-time collaboration, AI tools (translation, image/text generation, background removal), smart scaling to all standard ad sizes, project/brand organization, export to HTML5/MP4, and ad network integration.

Please design a full user flow and interface system with:
- Drag & drop editor and animation timeline
- Brand Kit builder (fonts, colors, logos)
- Spreadsheet import or form-based bulk banner generation
- Team collaboration (comments, approvals, roles)
- Publishing & scheduling to ad networks
- Export formats: HTML5, GIF, MP4
- Central asset library & project folders
- Real-time preview and performance dashboards
- Optional dev panel for custom code injection

Bonus features we’d like to include eventually:
- Template marketplace
- A/B testing
- WCAG accessibility assistant

Please include:
- Wireframes
- User flow diagrams
- Component design system

Design Guidelines:
- Modular layout with collapsible toolbars
- Timeline editor with playback controls
- Onboarding with guided tour and tooltips
- Responsive UI for desktop/tablet
- Undo/redo and version history
- Contextual real-time feedback during editing
- Accessibility and keyboard navigability

Tech Stack:
- Next.js v15
- Remotion (MP4 rendering)
- CSS animation layers
- State management (Zustand or Redux Toolkit)
- Integrations planned with GPT, image-generation APIs, and publishing platforms

Goal: A modern, scalable, and AI-enhanced ad creation experience that feels like a mix of Figma + Canva for HTML5 banners.
