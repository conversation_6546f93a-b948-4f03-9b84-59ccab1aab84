import { useState, useCallback, useRef, useEffect } from 'react';

export interface TimelineState {
  currentFrame: number;
  isPlaying: boolean;
  timelineZoom: number;
}

export interface UseTimelineStateReturn {
  // State
  currentFrame: number;
  isPlaying: boolean;
  timelineZoom: number;
  
  // Actions
  setCurrentFrame: (frame: number) => void;
  setIsPlaying: (playing: boolean) => void;
  setTimelineZoom: (zoom: number) => void;
  
  // Timeline controls
  play: () => void;
  pause: () => void;
  stop: () => void;
  skipToStart: () => void;
  skipToEnd: (totalFrames: number) => void;
  stepForward: (totalFrames: number) => void;
  stepBackward: () => void;
  
  // Frame management
  handleFrameChange: (newFrame: number, totalFrames: number, isLoopEnabled?: boolean) => void;
  handlePlayPause: () => void;
  
  // Save timeline zoom to storage
  saveTimelineZoom: (bannerId?: string, projectId?: string) => void;
}

export const useTimelineState = (
  initialFrame = 0,
  initialZoom = 100
): UseTimelineStateReturn => {
  // Ensure initial frame is always a valid number
  const validInitialFrame = isNaN(initialFrame) ? 0 : Math.max(0, initialFrame);
  const validInitialZoom = isNaN(initialZoom) ? 100 : Math.max(25, Math.min(500, initialZoom));
  
  const [currentFrame, setCurrentFrame] = useState(validInitialFrame);
  const [isPlaying, setIsPlaying] = useState(false);
  const [timelineZoom, setTimelineZoom] = useState(validInitialZoom);
  
  // Frame rate constant
  const FPS = 30;
  
  // Timeline control functions
  const play = useCallback(() => {
    setIsPlaying(true);
  }, []);

  const pause = useCallback(() => {
    setIsPlaying(false);
  }, []);

  const stop = useCallback(() => {
    setIsPlaying(false);
    setCurrentFrame(0);
  }, []);

  const skipToStart = useCallback(() => {
    setCurrentFrame(0);
    setIsPlaying(false);
  }, []);

  const skipToEnd = useCallback((totalFrames: number) => {
    const validTotalFrames = isNaN(totalFrames) ? 0 : totalFrames;
    setCurrentFrame(Math.max(0, validTotalFrames - 1));
    setIsPlaying(false);
  }, []);

  const stepForward = useCallback((totalFrames: number) => {
    const validTotalFrames = isNaN(totalFrames) ? 0 : totalFrames;
    setCurrentFrame(prev => {
      const validPrev = isNaN(prev) ? 0 : prev;
      return Math.min(validTotalFrames - 1, validPrev + FPS);
    });
    setIsPlaying(false);
  }, []);

  const stepBackward = useCallback(() => {
    setCurrentFrame(prev => {
      const validPrev = isNaN(prev) ? 0 : prev;
      return Math.max(0, validPrev - FPS);
    });
    setIsPlaying(false);
  }, []);

  // Handle frame changes with bounds checking and NaN protection
  const handleFrameChange = useCallback((
    newFrame: number, 
    totalFrames: number, 
    isLoopEnabled = false
  ) => {
    // Validate inputs
    const validNewFrame = isNaN(newFrame) ? 0 : newFrame;
    const validTotalFrames = isNaN(totalFrames) ? 1 : Math.max(1, totalFrames);
    
    if (isLoopEnabled) {
      // Handle looping
      const clampedFrame = validNewFrame >= validTotalFrames ? 0 : Math.max(0, validNewFrame);
      setCurrentFrame(clampedFrame);
    } else {
      // Clamp to bounds
      const clampedFrame = Math.max(0, Math.min(validTotalFrames - 1, validNewFrame));
      setCurrentFrame(clampedFrame);
      
      // Stop playing if we hit the end
      if (clampedFrame >= validTotalFrames - 1) {
        setIsPlaying(false);
      }
    }
  }, []);

  // Toggle play/pause
  const handlePlayPause = useCallback(() => {
    setIsPlaying(prev => !prev);
  }, []);

  // Save timeline zoom to localStorage for persistence
  const saveTimelineZoom = useCallback((bannerId?: string, projectId?: string) => {
    if (typeof window !== 'undefined' && bannerId) {
      try {
        const storageKey = `banner_${bannerId}_timeline_zoom`;
        localStorage.setItem(storageKey, timelineZoom.toString());
      } catch (error) {
        console.warn('Failed to save timeline zoom to localStorage:', error);
      }
    }
  }, [timelineZoom]);

  // Auto-save zoom changes
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      // This will be called by the editor when it has bannerId
      // saveTimelineZoom(bannerId, projectId);
    }, 1000);

    return () => clearTimeout(timeoutId);
  }, [timelineZoom]);

  // Protected setters that validate inputs
  const setCurrentFrameProtected = useCallback((frame: number | ((prev: number) => number)) => {
    setCurrentFrame(prev => {
      const newFrame = typeof frame === 'function' ? frame(prev) : frame;
      const validFrame = isNaN(newFrame) ? 0 : Math.max(0, newFrame);
      
      // Debug logging to catch NaN values
      if (isNaN(newFrame)) {
        console.warn('Timeline: Attempted to set currentFrame to NaN, defaulting to 0', {
          originalValue: newFrame,
          prev,
          stackTrace: new Error().stack
        });
      }
      
      return validFrame;
    });
  }, []);

  const setTimelineZoomProtected = useCallback((zoom: number | ((prev: number) => number)) => {
    setTimelineZoom(prev => {
      const newZoom = typeof zoom === 'function' ? zoom(prev) : zoom;
      const validZoom = isNaN(newZoom) ? 100 : Math.max(25, Math.min(500, newZoom));
      return validZoom;
    });
  }, []);

  return {
    // State - ensure no NaN values are returned
    currentFrame: isNaN(currentFrame) ? 0 : currentFrame,
    isPlaying,
    timelineZoom: isNaN(timelineZoom) ? 100 : timelineZoom,
    
    // Setters
    setCurrentFrame: setCurrentFrameProtected,
    setIsPlaying,
    setTimelineZoom: setTimelineZoomProtected,
    
    // Controls
    play,
    pause,
    stop,
    skipToStart,
    skipToEnd,
    stepForward,
    stepBackward,
    
    // Handlers
    handleFrameChange,
    handlePlayPause,
    
    // Persistence
    saveTimelineZoom,
  };
};