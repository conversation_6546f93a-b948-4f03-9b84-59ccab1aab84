'use client';

import { useToast } from '@/hooks/use-toast';

export const useCustomToast = () => {
  const { toast } = useToast();

  const showToast = ({ type = 'default', message, title }) => {
    const variants = {
      default: 'default',
      success: 'success',
      error: 'destructive',
      warning: 'warning',
      info: 'info'
    };

    // Use provided title or generate one based on type
    // For 'default' type, use 'Notification' instead of 'Default'
    const toastTitle = title || (
      type === 'default'
        ? 'Notification'
        : type.charAt(0).toUpperCase() + type.slice(1)
    );

    toast({
      title: toastTitle,
      description: message,
      variant: variants[type] || 'default',
      className: 'z-[100]'
    });
  };

  return { showToast };
};