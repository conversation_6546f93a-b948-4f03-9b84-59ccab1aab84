import { useState, useCallback } from 'react';

// Interface for banner element
export interface BannerElement {
  id: string;
  type: 'text' | 'image' | 'button' | 'shape' | 'rating' | 'qrcode';
  position: { x: number; y: number };
  width: number;
  height: number;
  rotation?: number;
  opacity?: number;
  zIndex: number;
  startFrame: number;
  visibilityDuration: number;
  animationIn?: string;
  animationOut?: string;
  animationDuration?: number;
  // Type-specific properties
  text?: string;
  src?: string;
  backgroundColor?: string;
  textColor?: string;
  fontSize?: number;
  fontFamily?: string;
  fontWeight?: string;
  textAlign?: string;
  letterSpacing?: number;
  lineHeight?: number;
  borderRadius?: number;
  url?: string;
  openInNewTab?: boolean;
  shapeType?: 'circle' | 'rectangle' | 'triangle';
  maxStars?: number;
  value?: number;
  color?: string;
  size?: number;
  foregroundColor?: string;
}

// Interface for banner metadata
export interface BannerMetadata {
  id: string | null;
  name: string;
  projectId: string | null;
  bannerOutputUrl?: string;
}

// Interface for banner appearance settings
export interface BannerAppearance {
  backgroundColor: string;
  gradientType: string;
  gradientColor1: string;
  gradientColor2: string;
  gradientAngle: number;
  borderStyle: string;
  borderWidth: number;
  borderColor: string;
  borderRadius: number;
  boxShadow: boolean;
  shadowColor: string;
  shadowBlur: number;
  shadowSpread: number;
  shadowX: number;
  shadowY: number;
}

// Interface for animation settings
export interface AnimationSettings {
  totalBannerPlaybackDurationInSeconds: number;
  frameRate: number;
  animationSpeed: number;
  isLoopEnabled: boolean;
  autoReplay: boolean;
}

// Interface for the complete banner state
export interface BannerState {
  // Core banner data
  banner: BannerMetadata | null;
  elements: BannerElement[];
  selectedElement: BannerElement | null;
  
  // Appearance settings
  appearance: BannerAppearance;
  
  // Animation settings
  animation: AnimationSettings;
  
  // History management
  history: BannerElement[][];
  historyIndex: number;
}

// Default values
const defaultAppearance: BannerAppearance = {
  backgroundColor: '#ffffff',
  gradientType: 'none',
  gradientColor1: '#4F46E5',
  gradientColor2: '#7C3AED',
  gradientAngle: 45,
  borderStyle: 'none',
  borderWidth: 0,
  borderColor: '#000000',
  borderRadius: 0,
  boxShadow: false,
  shadowColor: '#000000',
  shadowBlur: 10,
  shadowSpread: 0,
  shadowX: 0,
  shadowY: 0,
};

const defaultAnimation: AnimationSettings = {
  totalBannerPlaybackDurationInSeconds: 5,
  frameRate: 30,
  animationSpeed: 1,
  isLoopEnabled: false,
  autoReplay: false,
};

const defaultBannerState: BannerState = {
  banner: null,
  elements: [],
  selectedElement: null,
  appearance: defaultAppearance,
  animation: defaultAnimation,
  history: [[]],
  historyIndex: 0,
};

export interface UseBannerStateReturn {
  // State
  state: BannerState;
  
  // Banner metadata actions
  setBanner: (banner: BannerMetadata | null) => void;
  setBannerName: (name: string) => void;
  
  // Element actions
  setElements: (elements: BannerElement[]) => void;
  addElement: (element: BannerElement) => void;
  updateElement: (elementId: string, updates: Partial<BannerElement>) => void;
  deleteElement: (elementId: string) => void;
  setSelectedElement: (element: BannerElement | null) => void;
  
  // Appearance actions
  updateAppearance: (updates: Partial<BannerAppearance>) => void;
  setBackgroundColor: (color: string) => void;
  
  // Animation actions
  updateAnimation: (updates: Partial<AnimationSettings>) => void;
  setTotalBannerPlaybackDurationInSeconds: (duration: number) => void;
  setIsLoopEnabled: (enabled: boolean) => void;
  setAutoReplay: (enabled: boolean) => void;
  setAnimationSpeed: (speed: number) => void;
  setFrameRate: (rate: number) => void;
  
  // History actions
  updateElementsWithHistory: (newElements: BannerElement[], shouldAddToHistory?: boolean) => void;
  undo: () => void;
  redo: () => void;
  canUndo: boolean;
  canRedo: boolean;
  
  // Utility actions
  resetState: () => void;
  initializeFromBanner: (bannerData: any) => void;
}

export const useBannerState = (): UseBannerStateReturn => {
  const [state, setState] = useState<BannerState>(defaultBannerState);

  // Banner metadata actions
  const setBanner = useCallback((banner: BannerMetadata | null) => {
    setState(prev => ({ ...prev, banner }));
  }, []);

  const setBannerName = useCallback((name: string) => {
    console.log('🏷️ Setting banner name to:', name);
    setState(prev => {
      const newBanner = prev.banner 
        ? { ...prev.banner, name } 
        : { id: null, name, projectId: null, userId: null, createdAt: null, updatedAt: null };
      
      console.log('🏷️ Banner name updated from', prev.banner?.name, 'to', newBanner.name);
      
      return {
        ...prev,
        banner: newBanner
      };
    });
  }, []);

  // Element actions
  const setElements = useCallback((elements: BannerElement[]) => {
    setState(prev => ({ ...prev, elements }));
  }, []);

  const addElement = useCallback((element: BannerElement) => {
    setState(prev => ({
      ...prev,
      elements: [...prev.elements, element],
      selectedElement: element
    }));
  }, []);

  const updateElement = useCallback((elementId: string, updates: Partial<BannerElement>) => {
    setState(prev => ({
      ...prev,
      elements: prev.elements.map(el => 
        el.id === elementId ? { ...el, ...updates } : el
      ),
      selectedElement: prev.selectedElement?.id === elementId 
        ? { ...prev.selectedElement, ...updates }
        : prev.selectedElement
    }));
  }, []);

  const deleteElement = useCallback((elementId: string) => {
    setState(prev => ({
      ...prev,
      elements: prev.elements.filter(el => el.id !== elementId),
      selectedElement: prev.selectedElement?.id === elementId ? null : prev.selectedElement
    }));
  }, []);

  const setSelectedElement = useCallback((element: BannerElement | null) => {
    setState(prev => ({ ...prev, selectedElement: element }));
  }, []);

  // Appearance actions
  const updateAppearance = useCallback((updates: Partial<BannerAppearance>) => {
    setState(prev => ({
      ...prev,
      appearance: { ...prev.appearance, ...updates }
    }));
  }, []);

  const setBackgroundColor = useCallback((color: string) => {
    updateAppearance({ backgroundColor: color });
  }, [updateAppearance]);

  // Animation actions
  const updateAnimation = useCallback((updates: Partial<AnimationSettings>) => {
    setState(prev => ({
      ...prev,
      animation: { ...prev.animation, ...updates }
    }));
  }, []);

  const setTotalBannerPlaybackDurationInSeconds = useCallback((duration: number) => {
    updateAnimation({ totalBannerPlaybackDurationInSeconds: duration });
  }, [updateAnimation]);

  const setIsLoopEnabled = useCallback((enabled: boolean) => {
    updateAnimation({ isLoopEnabled: enabled });
  }, [updateAnimation]);

  const setAutoReplay = useCallback((enabled: boolean) => {
    updateAnimation({ autoReplay: enabled });
  }, [updateAnimation]);

  const setAnimationSpeed = useCallback((speed: number) => {
    updateAnimation({ animationSpeed: speed });
  }, [updateAnimation]);

  const setFrameRate = useCallback((rate: number) => {
    updateAnimation({ frameRate: rate });
  }, [updateAnimation]);

  // History actions
  const updateElementsWithHistory = useCallback((newElements: BannerElement[], shouldAddToHistory = true) => {
    setState(prev => {
      if (!shouldAddToHistory) {
        return { ...prev, elements: newElements };
      }

      // Add to history
      const newHistory = prev.history.slice(0, prev.historyIndex + 1);
      newHistory.push([...newElements]);
      
      // Limit history size to prevent memory issues
      const maxHistorySize = 50;
      if (newHistory.length > maxHistorySize) {
        newHistory.shift();
      }

      return {
        ...prev,
        elements: newElements,
        history: newHistory,
        historyIndex: newHistory.length - 1
      };
    });
  }, []);

  const undo = useCallback(() => {
    setState(prev => {
      if (prev.historyIndex > 0) {
        const newIndex = prev.historyIndex - 1;
        return {
          ...prev,
          elements: [...prev.history[newIndex]],
          historyIndex: newIndex,
          selectedElement: null // Clear selection on undo
        };
      }
      return prev;
    });
  }, []);

  const redo = useCallback(() => {
    setState(prev => {
      if (prev.historyIndex < prev.history.length - 1) {
        const newIndex = prev.historyIndex + 1;
        return {
          ...prev,
          elements: [...prev.history[newIndex]],
          historyIndex: newIndex,
          selectedElement: null // Clear selection on redo
        };
      }
      return prev;
    });
  }, []);

  // History capabilities
  const canUndo = state.historyIndex > 0;
  const canRedo = state.historyIndex < state.history.length - 1;

  // Utility actions
  const resetState = useCallback(() => {
    setState(defaultBannerState);
  }, []);

  const initializeFromBanner = useCallback((bannerData: any) => {
    const newState: BannerState = {
      banner: {
        id: bannerData.id || null,
        name: bannerData.name || 'Untitled Banner',
        projectId: bannerData.projectId || null,
        bannerOutputUrl: bannerData.bannerOutputUrl
      },
      elements: Array.isArray(bannerData.elements) ? bannerData.elements : [],
      selectedElement: null,
      appearance: {
        backgroundColor: bannerData.backgroundColor || defaultAppearance.backgroundColor,
        gradientType: bannerData.gradientType || defaultAppearance.gradientType,
        gradientColor1: bannerData.gradientColor1 || defaultAppearance.gradientColor1,
        gradientColor2: bannerData.gradientColor2 || defaultAppearance.gradientColor2,
        gradientAngle: bannerData.gradientAngle || defaultAppearance.gradientAngle,
        borderStyle: bannerData.borderStyle || defaultAppearance.borderStyle,
        borderWidth: bannerData.borderWidth || defaultAppearance.borderWidth,
        borderColor: bannerData.borderColor || defaultAppearance.borderColor,
        borderRadius: bannerData.borderRadius || defaultAppearance.borderRadius,
        boxShadow: bannerData.boxShadow || defaultAppearance.boxShadow,
        shadowColor: bannerData.shadowColor || defaultAppearance.shadowColor,
        shadowBlur: bannerData.shadowBlur || defaultAppearance.shadowBlur,
        shadowSpread: bannerData.shadowSpread || defaultAppearance.shadowSpread,
        shadowX: bannerData.shadowX || defaultAppearance.shadowX,
        shadowY: bannerData.shadowY || defaultAppearance.shadowY,
      },
      animation: {
        totalBannerPlaybackDurationInSeconds: bannerData.totalBannerPlaybackDurationInSeconds || defaultAnimation.totalBannerPlaybackDurationInSeconds,
        frameRate: bannerData.frameRate || defaultAnimation.frameRate,
        animationSpeed: bannerData.animationSpeed || defaultAnimation.animationSpeed,
        isLoopEnabled: bannerData.isLoopEnabled || defaultAnimation.isLoopEnabled,
        autoReplay: bannerData.autoReplay || defaultAnimation.autoReplay,
      },
      history: [bannerData.elements ? [...bannerData.elements] : []],
      historyIndex: 0,
    };

    setState(newState);
  }, []);

  return {
    state,
    setBanner,
    setBannerName,
    setElements,
    addElement,
    updateElement,
    deleteElement,
    setSelectedElement,
    updateAppearance,
    setBackgroundColor,
    updateAnimation,
    setTotalBannerPlaybackDurationInSeconds,
    setIsLoopEnabled,
    setAutoReplay,
    setAnimationSpeed,
    setFrameRate,
    updateElementsWithHistory,
    undo,
    redo,
    canUndo,
    canRedo,
    resetState,
    initializeFromBanner,
  };
};