/**
 * Common fetcher utility for SWR that handles authentication and error handling
 * 
 * @param {string} url - The URL to fetch
 * @returns {Promise<any>} - The parsed JSON response
 */
export const fetcher = async (url) => {
  console.log('Fetching data from:', url);
  try {
    const res = await fetch(url, {
      credentials: 'include',
      cache: 'no-store' // Prevent caching to avoid stale data
    });
    
    if (!res.ok) {
      const error = await res.json();
      console.error('Fetch error:', error);
      throw new Error(error.details || error.error || 'Failed to fetch');
    }
    
    const data = await res.json();
    return data;
  } catch (error) {
    console.error('Fetch exception:', error);
    throw error;
  }
};
