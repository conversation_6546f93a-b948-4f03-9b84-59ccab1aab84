/**
 * Generate a UUID v4 (random) client-side
 * This is used for optimistic UI updates before server response
 * @returns {string} A UUID v4 string
 */
export function generateUUID() {
  // RFC4122 compliant UUID v4 implementation
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}
