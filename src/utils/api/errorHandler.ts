/**
 * src/utils/api/errorHandler.js
 * Utility functions for consistent API error handling
 */

import { NextResponse } from 'next/server';

// Define common error types
export const ERROR_TYPES = {
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409, // e.g., duplicate resource
  UNPROCESSABLE_ENTITY: 422, // Validation errors
  INTERNAL_SERVER_ERROR: 500,
  DATABASE_ERROR: 503, // Service Unavailable (Database issue)
};

/**
 * Creates a standardized error response for API routes.
 * Logs the error server-side for debugging.
 *
 * @param {string} message - User-friendly error message.
 * @param {number} status - HTTP status code (use ERROR_TYPES).
 * @param {Object|string|null} [details=null] - Additional error details (logged server-side, optionally sent to client).
 * @param {Error|null} [originalError=null] - The original error object for logging.
 * @returns {NextResponse} - Formatted error response.
 */
export function createErrorResponse(message, status = ERROR_TYPES.INTERNAL_SERVER_ERROR, details = null, originalError = null) {
  // Log the detailed error server-side
  console.error(`API Error (${status}): ${message}`, {
    details: details || (originalError ? originalError.message : 'No details'),
    stack: originalError?.stack,
  });

  // Prepare the client-safe response body
  const responseBody = {
    error: message,
    // Optionally include non-sensitive details for the client
    ...(process.env.NODE_ENV !== 'production' && details ? { details } : {}),
  };

  return NextResponse.json(responseBody, { status });
}

/**
 * Creates a standardized success response for API routes.
 *
 * @param {Object} data - Response data.
 * @param {number} [status=200] - HTTP status code.
 * @returns {NextResponse} - Formatted success response.
 */
export function createSuccessResponse(data, status = 200) {
  return NextResponse.json(data, { status });
}

/**
 * Handles Supabase errors and returns an appropriate standardized error response.
 *
 * @param {Object} error - Supabase error object { code, message, details, hint }.
 * @param {string} [contextMessage='Database operation failed'] - Context-specific message prefix.
 * @returns {NextResponse} - Formatted error response using createErrorResponse.
 */
export function handleSupabaseError(error, contextMessage = 'Database operation failed') {
  let status = ERROR_TYPES.DATABASE_ERROR;
  let message = `${contextMessage}: An unexpected database error occurred.`;
  let logDetails = error; // Log the full Supabase error object

  // Map common Supabase error codes to HTTP statuses and user messages
  switch (error.code) {
    case 'PGRST116': // "Resource not found"
      status = ERROR_TYPES.NOT_FOUND;
      message = `${contextMessage}: Resource not found.`;
      break;
    case '23505': // "Unique violation"
      status = ERROR_TYPES.CONFLICT;
      message = `${contextMessage}: A resource with this identifier already exists.`;
      break;
    case '23503': // "Foreign key violation"
      status = ERROR_TYPES.BAD_REQUEST;
      message = `${contextMessage}: Invalid reference to another resource.`;
      break;
    case '42P01': // "Undefined table"
      status = ERROR_TYPES.INTERNAL_SERVER_ERROR;
      message = 'Internal Server Error: Database configuration issue.';
      logDetails = 'Database table not found. Check migrations.'; // More specific log
      break;
    case '42501': // "Permission denied"
      status = ERROR_TYPES.FORBIDDEN;
      message = `${contextMessage}: Permission denied. Check RLS policies.`;
      break;
    case '42703': // "Undefined column"
      status = ERROR_TYPES.INTERNAL_SERVER_ERROR;
      message = 'Internal Server Error: Database schema mismatch.';
      logDetails = 'Database column not found. Check schema.';
      break;
    // Add more specific Supabase error codes as needed
    default:
      // Use Supabase message if available, otherwise keep the default
      if (error.message) {
        message = `${contextMessage}: ${error.message}`;
      }
  }

  // Use the standardized error response creator
  return createErrorResponse(message, status, logDetails, error);
}

/**
 * Wraps an API handler with try/catch and consistent error handling.
 *
 * @param {Function} handler - API handler function (async (req, ...args) => NextResponse).
 * @returns {Function} - Wrapped handler function.
 */
export function withErrorHandling(handler) {
  return async (req, ...args) => {
    try {
      // Execute the original handler
      return await handler(req, ...args);
    } catch (error) {
      // Handle known error types (like validation errors thrown manually)
      if (error.status && error.message) {
        return createErrorResponse(error.message, error.status, error.details, error);
      }
      // Handle Supabase errors specifically
      if (error.code && error.message) {
        return handleSupabaseError(error);
      }
      // Handle generic errors
      return createErrorResponse(
        'An unexpected internal server error occurred.',
        ERROR_TYPES.INTERNAL_SERVER_ERROR,
        error.message, // Provide original message as details for logging
        error
      );
    }
  };
}
