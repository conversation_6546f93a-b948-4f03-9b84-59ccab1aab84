import { FORMATS, DEFAULT_FORMAT_KEY, getDefaultFormat } from '@/constants/formats';

/**
 * Find a format by its dimensions
 * @param {number} width - The width to search for
 * @param {number} height - The height to search for
 * @returns {string|null} The format key if found, null otherwise
 */
export function findFormatByDimensions(width, height) {
  if (!width || !height || isNaN(width) || isNaN(height)) {
    console.warn('Invalid dimensions provided to findFormatByDimensions', { width, height });
    return null;
  }

  // Ensure width and height are numbers
  const numWidth = Number(width);
  const numHeight = Number(height);

  // Check built-in formats
  for (const [key, format] of Object.entries(FORMATS)) {
    if (format.width === numWidth && format.height === numHeight) {
      console.log(`Found matching standard format: ${key} for dimensions ${numWidth}x${numHeight}`);
      return key;
    }
  }

  // If no match found, return null
  console.log(`No standard format found for dimensions: ${numWidth}x${numHeight}`);
  return null;
}

/**
 * Normalize format information to ensure consistent format data
 * @param {Object} data - The data object containing format information
 * @returns {Object} The normalized data object
 */
export function normalizeFormatData(data) {
  if (!data) return null;

  const result = { ...data };

  // If we have a formatKey, use it to set dimensions
  if (result.formatKey && FORMATS[result.formatKey]) {
    result.formatWidth = FORMATS[result.formatKey].width;
    result.formatHeight = FORMATS[result.formatKey].height;
    // Also set width/height for compatibility
    result.width = result.formatWidth;
    result.height = result.formatHeight;
  }
  // If we have dimensions but no formatKey, try to find a matching format
  else if (result.formatWidth && result.formatHeight) {
    const formatKey = findFormatByDimensions(result.formatWidth, result.formatHeight);
    if (formatKey) {
      result.formatKey = formatKey;
    } else {
      // Create a custom format key
      result.formatKey = `${result.formatWidth}x${result.formatHeight}`;
    }
    // Also set width/height for compatibility
    result.width = result.formatWidth;
    result.height = result.formatHeight;
  }
  // If we have width/height but no formatWidth/formatHeight
  else if (result.width && result.height) {
    result.formatWidth = result.width;
    result.formatHeight = result.height;
    const formatKey = findFormatByDimensions(result.width, result.height);
    if (formatKey) {
      result.formatKey = formatKey;
    } else {
      // Create a custom format key
      result.formatKey = `${result.width}x${result.height}`;
    }
  }
  // If we have nothing, use default format
  else {
    const defaultFormat = getDefaultFormat();
    result.formatKey = DEFAULT_FORMAT_KEY;
    result.formatWidth = defaultFormat.width;
    result.formatHeight = defaultFormat.height;
    result.width = defaultFormat.width;
    result.height = defaultFormat.height;
  }

  return result;
}

/**
 * Get format dimensions from a format key
 * @param {string} formatKey - The format key
 * @param {Array} customFormats - Optional array of custom formats
 * @returns {Object} The format dimensions {width, height}
 */
export function getFormatDimensions(formatKey, customFormats = []) {
  // Check built-in formats
  if (FORMATS[formatKey]) {
    return {
      width: FORMATS[formatKey].width,
      height: FORMATS[formatKey].height
    };
  }

  // Check custom formats
  if (customFormats && customFormats.length > 0) {
    const customFormat = customFormats.find(f => f.key === formatKey);
    if (customFormat) {
      return {
        width: customFormat.width,
        height: customFormat.height
      };
    }
  }

  // Try to parse dimensions from format key (e.g., "300x250")
  if (formatKey && formatKey.includes('x')) {
    const [width, height] = formatKey.split('x').map(Number);
    if (!isNaN(width) && !isNaN(height)) {
      return { width, height };
    }
  }

  // Fallback to default format
  const defaultFormat = getDefaultFormat();
  return {
    width: defaultFormat.width,
    height: defaultFormat.height
  };
}

/**
 * Combine built-in formats with custom formats
 * @param {Array} customFormats - Array of custom formats
 * @returns {Object} Combined formats object
 */
export function getAllFormats(customFormats = []) {
  const allFormats = { ...FORMATS };

  if (customFormats && customFormats.length > 0) {
    customFormats.forEach(format => {
      if (format && format.key) {
        allFormats[format.key] = {
          width: format.width,
          height: format.height,
          label: format.name || `${format.width}x${format.height}`,
          isCustom: true
        };
      }
    });
  }

  return allFormats;
}

/**
 * Validate format data
 * @param {Object} formatData - The format data to validate
 * @returns {Object} Validation result {isValid, errors}
 */
export function validateFormatData(formatData) {
  const errors = [];

  if (!formatData) {
    errors.push('Format data is required');
    return { isValid: false, errors };
  }

  if (!formatData.formatKey) {
    errors.push('Format key is required');
  }

  if (!formatData.formatWidth || typeof formatData.formatWidth !== 'number') {
    errors.push('Format width must be a valid number');
  }

  if (!formatData.formatHeight || typeof formatData.formatHeight !== 'number') {
    errors.push('Format height must be a valid number');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}
