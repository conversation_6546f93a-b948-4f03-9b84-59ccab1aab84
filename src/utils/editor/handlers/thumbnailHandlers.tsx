import { toast } from '@/hooks/use-toast';
import { generateBannerThumbnail, uploadThumbnail } from '@/utils/dashboard/thumbnailGenerator';

interface Banner {
  id: string;
  width?: number;
  height?: number;
  [key: string]: any;
}

/**
 * Manually generate and upload a thumbnail for a banner
 * @param banner - The banner object
 * @param setIsGeneratingThumbnail - State setter for loading state
 * @returns Whether the thumbnail was successfully generated
 */
export const generateBannerThumbnailManually = async (
  banner: Banner,
  setIsGeneratingThumbnail?: (isGenerating: boolean) => void
): Promise<boolean> => {
  if (setIsGeneratingThumbnail) {
    setIsGeneratingThumbnail(true);
  }

  try {
    console.log('Generating thumbnail for banner:', banner.id);

    // Find the canvas element
    const canvasElement = document.querySelector('.banner-canvas') as HTMLElement;
    if (!canvasElement) {
      toast({
        title: 'Thumbnail Generation Failed',
        description: 'Could not find the banner canvas',
        variant: 'destructive',
      });
      return false;
    }

    // Make sure we have the banner dimensions without mutating the original banner
    const bannerWithSize = {
      ...banner,
      width:
        banner.width ??
        (canvasElement.offsetWidth || canvasElement.clientWidth),
      height:
        banner.height ??
        (canvasElement.offsetHeight || canvasElement.clientHeight),
    };

    if (!banner.width || !banner.height) {
      console.log(`Using canvas dimensions: ${bannerWithSize.width}x${bannerWithSize.height}`);
    }

    // Wait a moment to ensure all elements are rendered
    await new Promise(resolve => setTimeout(resolve, 100));

    // Generate and upload in one step
    const thumbnailDataUrl = await generateBannerThumbnail(bannerWithSize, canvasElement);
    if (!thumbnailDataUrl) {
      toast({
        title: 'Thumbnail Generation Failed',
        description: 'Could not create the thumbnail image',
        variant: 'destructive',
      });
      return false;
    }

    // Upload the thumbnail
    const result = await uploadThumbnail(thumbnailDataUrl, bannerWithSize.id, 'banner', true);
    if (!result) {
      toast({
        title: 'Thumbnail Upload Failed',
        description: 'Could not upload the thumbnail',
        variant: 'destructive',
      });
      return false;
    }

    toast({
      title: 'Thumbnail Generated',
      description: 'Banner thumbnail has been updated',
      variant: 'success',
    });

    return true;
  } catch (error) {
    console.error('Thumbnail generation error:', error);
    toast({
      title: 'Error',
      description: 'Failed to generate thumbnail',
      variant: 'destructive',
    });
    return false;
  } finally {
    if (setIsGeneratingThumbnail) {
      setIsGeneratingThumbnail(false);
    }
  }
};
