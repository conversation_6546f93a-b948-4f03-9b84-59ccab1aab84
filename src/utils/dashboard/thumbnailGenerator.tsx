import * as htmlToImage from 'html-to-image';

interface ThumbnailOptions {
  width?: number;
  height?: number;
  quality?: number;
  format?: 'png' | 'jpeg';
}

/**
 * Generates a thumbnail from a DOM element
 * @param element - The DOM element to capture
 * @param options - Options for the thumbnail generation
 * @returns A promise that resolves to the thumbnail data URL
 */
export async function generateThumbnail(element: HTMLElement, options: ThumbnailOptions = {}): Promise<string | null> {
  if (!element) {
    console.error('No element provided for thumbnail generation');
    return null;
  }

  const {
    width = 300,
    height = 200,
    quality = 0.8,
    format = 'jpeg'
  } = options;

  try {
    console.log(`Generating ${format} thumbnail with dimensions ${width}x${height}`);

    // Enhanced options for html-to-image
    const imageOptions = {
      quality,
      width,
      height,
      pixelRatio: 2, // Higher pixel ratio for better quality
      backgroundColor: '#ffffff', // Ensure white background
      style: {
        // Force visibility
        visibility: 'visible',
        opacity: '1',
        // Ensure proper rendering
        display: 'block',
        overflow: 'visible'
      },
      filter: (node) => {
        // Skip elements with class 'no-thumbnail'
        if (node.classList && node.classList.contains('no-thumbnail')) {
          return false;
        }
        return true;
      }
    };

    // Generate the image with proper error handling
    let dataUrl;
    try {
      if (format === 'png') {
        dataUrl = await htmlToImage.toPng(element, imageOptions);
      } else {
        dataUrl = await htmlToImage.toJpeg(element, imageOptions);
      }
    } catch (captureError) {
      console.error('Error in html-to-image capture:', captureError);
      // Try with simpler options as fallback
      const fallbackOptions = {
        quality,
        width,
        height,
        backgroundColor: '#ffffff'
      };

      dataUrl = format === 'png'
        ? await htmlToImage.toPng(element, fallbackOptions)
        : await htmlToImage.toJpeg(element, fallbackOptions);
    }

    console.log('Thumbnail generated successfully');
    return dataUrl;
  } catch (error) {
    console.error('Error generating thumbnail:', error);
    return null;
  }
}

interface BannerData {
  width?: number;
  height?: number;
  formatWidth?: number;
  formatHeight?: number;
}

/**
 * Generates a thumbnail from a banner or template preview
 * @param data - The banner or template data
 * @param previewElement - The preview element to capture
 * @returns A promise that resolves to the thumbnail data URL
 */
export async function generateBannerThumbnail(data: BannerData, previewElement: HTMLElement): Promise<string | null> {
  if (!data || !previewElement) {
    console.error('Missing data or preview element for banner thumbnail generation');
    return null;
  }

  // Determine dimensions based on the banner format
  const width = data.width || data.formatWidth || 300;
  const height = data.height || data.formatHeight || 200;

  // Calculate thumbnail dimensions while maintaining aspect ratio
  const maxThumbnailSize = 400;
  const aspectRatio = width / height;

  let thumbnailWidth, thumbnailHeight;

  if (width > height) {
    // Landscape orientation
    thumbnailWidth = Math.min(width, maxThumbnailSize);
    thumbnailHeight = Math.round(thumbnailWidth / aspectRatio);
  } else {
    // Portrait or square orientation
    thumbnailHeight = Math.min(height, maxThumbnailSize);
    thumbnailWidth = Math.round(thumbnailHeight * aspectRatio);
  }

  console.log(`Banner dimensions: ${width}x${height}, Thumbnail: ${thumbnailWidth}x${thumbnailHeight}`);

  // Instead of cloning, we'll use the original element but save its styles
  const originalStyles = {};
  const stylesToSave = [
    'width', 'height', 'transform', 'position', 'left', 'top',
    'visibility', 'opacity', 'overflow', 'backgroundColor'
  ];

  // Save original styles
  stylesToSave.forEach(prop => {
    originalStyles[prop] = previewElement.style[prop];
  });

  // Get computed background color
  const computedStyle = window.getComputedStyle(previewElement);
  const backgroundColor = computedStyle.backgroundColor;

  // Temporarily modify the element for capture
  previewElement.style.width = `${width}px`;
  previewElement.style.height = `${height}px`;
  previewElement.style.transform = 'none';
  previewElement.style.overflow = 'visible';

  // Make sure background color is preserved
  if (backgroundColor && backgroundColor !== 'rgba(0, 0, 0, 0)') {
    previewElement.style.backgroundColor = backgroundColor;
  } else {
    // Default to white if no background color
    previewElement.style.backgroundColor = '#ffffff';
  }

  // Save child element styles
  const childElements = previewElement.querySelectorAll('*');
  const childOriginalStyles = [];

  childElements.forEach(el => {
    const styles = {};
    childOriginalStyles.push({ element: el, styles });

    // Save important styles
    ['visibility', 'opacity', 'display'].forEach(prop => {
      styles[prop] = (el as HTMLElement).style[prop];
    });

    // Make sure all elements are visible
    (el as HTMLElement).style.visibility = 'visible';
    (el as HTMLElement).style.opacity = '1';
  });

  try {
    // Generate the thumbnail using the original element
    const thumbnail = await generateThumbnail(previewElement, {
      width: thumbnailWidth,
      height: thumbnailHeight,
      quality: 0.7,
      format: 'jpeg'
    });

    return thumbnail;
  } catch (error) {
    console.error('Error generating banner thumbnail:', error);
    return null;
  } finally {
    // Restore original styles
    stylesToSave.forEach(prop => {
      previewElement.style[prop] = originalStyles[prop];
    });

    // Restore child element styles
    childOriginalStyles.forEach(item => {
      const el = item.element;
      const styles = item.styles;

      Object.keys(styles).forEach(prop => {
        el.style[prop] = styles[prop];
      });
    });
  }
}

interface ThumbnailUploadResult {
  success?: boolean;
  url?: string;
  error?: string;
  details?: string;
}

/**
 * Uploads a thumbnail to the server
 * @param dataUrl - The data URL of the thumbnail
 * @param id - The ID of the banner or template
 * @param type - The type of the thumbnail ('banner' or 'template')
 * @param nonBlocking - Whether to use non-blocking mode (default: true)
 * @returns A promise that resolves to the upload response
 */
export async function uploadThumbnail(
  dataUrl: string,
  id: string,
  type: 'banner' | 'template' = 'banner',
  nonBlocking = true
): Promise<ThumbnailUploadResult | null> {
  if (!dataUrl || !id) {
    console.error('Missing data URL or ID for thumbnail upload');
    return null;
  }

  try {
    console.log(`Uploading thumbnail for ${type} ${id} (nonBlocking: ${nonBlocking})`);

    // Convert data URL to blob
    const blob = await fetch(dataUrl).then(res => res.blob());

    // Create form data
    const formData = new FormData();
    // Use .jpg extension since we're now using JPEG format
    formData.append('thumbnail', blob, `${id}.jpg`);
    formData.append('id', id);
    formData.append('type', type);

    // Add nonBlocking flag to form data
    formData.append('nonBlocking', nonBlocking.toString());

    // Upload the thumbnail
    const response = await fetch(`/api/${type}s/${id}/thumbnail`, {
      method: 'POST',
      credentials: 'include',
      body: formData
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.details || error.error || 'Failed to upload thumbnail');
    }

    const result = await response.json();

    if (nonBlocking) {
      console.log('Thumbnail processing started in background:', result);
    } else {
      console.log('Thumbnail uploaded successfully:', result);
    }

    return result;
  } catch (error) {
    console.error('Error uploading thumbnail:', error);
    return null;
  }
}
