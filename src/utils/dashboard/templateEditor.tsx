// Format utilities are imported dynamically where needed
import { generateBannerThumbnail, uploadThumbnail } from '@/utils/dashboard/thumbnailGenerator';

/**
 * Loads a template for editing in the editor
 * @param {string} templateId - The ID of the template to load
 * @returns {Promise<Object>} The template data with elements parsed from JSON
 */
export async function loadTemplateForEditor(templateId) {
  const response = await fetch(`/api/templates/${templateId}`, {
    credentials: 'include'
  });
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.details || error.error || 'Failed to load template');
  }

  const template = await response.json();

  // Normalize format information to ensure consistency
  const { normalizeFormatData } = await import('@/utils/editor/formatUtils');
  const normalizedTemplate = normalizeFormatData(template);

  // Parse elements from JSON string to array
  return {
    ...normalizedTemplate,
    elements: JSON.parse(normalizedTemplate.elements || '[]'),
  };
}

/**
 * Saves template changes from the editor
 * @param {string} templateId - The ID of the template being edited
 * @param {Object} editorState - The current state of the editor
 * @returns {Promise<Object>} The updated template data
 */
export async function saveTemplateFromEditor(templateId, editorState) {
  if (!templateId) {
    throw new Error('Template ID is required');
  }

  if (!editorState || typeof editorState !== 'object') {
    throw new Error('Editor state must be a valid object');
  }

  // Only include fields that are actually edited in the editor
  const payload = {};

  // Elements are always updated in editor
  if (editorState.elements) {
    // If elements is an array, stringify it
    if (Array.isArray(editorState.elements)) {
      (payload as any).elements = JSON.stringify(editorState.elements);
      console.log('Stringified elements array for template save');
    }
    // If it's already a string, make sure it's valid JSON
    else if (typeof editorState.elements === 'string') {
      try {
        // Test if it's valid JSON by parsing and re-stringifying
        const parsed = JSON.parse(editorState.elements);
        (payload as any).elements = JSON.stringify(parsed);
        console.log('Validated and re-stringified elements JSON');
      } catch (e) {
        console.error('Invalid elements JSON in editor state:', e);
        // If invalid, set to empty array
        (payload as any).elements = '[]';
      }
    }
    // If it's neither an array nor a string, set to empty array
    else {
      console.warn('Elements is neither array nor string, defaulting to empty array');
      (payload as any).elements = '[]';
    }
  } else {
    console.warn('No elements in editor state, defaulting to empty array');
    (payload as any).elements = '[]';
  }

  // Name can be updated in editor
  if (editorState.name !== undefined) {
    (payload as any).name = editorState.name;
    console.log('Including name in template save payload:', editorState.name);
  }

  // Background color is editable in editor
  if (editorState.backgroundColor !== undefined) {
    (payload as any).backgroundColor = editorState.backgroundColor;
  }

  // Animation settings are editable in editor
  if (editorState.totalBannerPlaybackDurationInSeconds !== undefined) {
    (payload as any).animationDuration = editorState.totalBannerPlaybackDurationInSeconds;
  }
  if (editorState.frameRate !== undefined) {
    (payload as any).frameRate = editorState.frameRate;
  }

  // Format information is editable in editor
  const formatKey = editorState.format?.formatKey || editorState.formatKey;
  if (formatKey) {
    // Use our format utilities to ensure consistent format data
    const { getFormatDimensions } = await import('@/utils/editor/formatUtils');
    const dimensions = getFormatDimensions(formatKey);

    (payload as any).formatKey = formatKey;
    (payload as any).formatWidth = dimensions.width;
    (payload as any).formatHeight = dimensions.height;

    // Also update width/height for compatibility
    (payload as any).width = dimensions.width;
    (payload as any).height = dimensions.height;
  }

  console.log('Saving template with payload:', payload);

  const response = await fetch(`/api/templates/${templateId}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    credentials: 'include',
    body: JSON.stringify(payload),
  });

  if (!response.ok) {
    const error = await response.json();
    console.error('Template save failed:', error);
    throw new Error(error.details || error.error || 'Failed to save template');
  }

  const savedTemplate = await response.json();
  console.log('Template saved successfully:', savedTemplate);

  // Generate and upload thumbnail if we have a canvas element
  // This is now done in a non-blocking way after the save is complete
  setTimeout(async () => {
    try {
      // Find the canvas element - we'll use this to generate the thumbnail
      const canvasElement = document.querySelector('.banner-canvas');
      if (canvasElement) {
        console.log('Generating thumbnail for template in background');

        // Generate the thumbnail
        const thumbnailDataUrl = await generateBannerThumbnail(
          savedTemplate,
          canvasElement as HTMLElement
        );

        if (thumbnailDataUrl) {
          // Upload the thumbnail with nonBlocking=true
          const nonBlocking = true; // Use non-blocking mode
          const uploadResult = await uploadThumbnail(
            thumbnailDataUrl,
            templateId,
            'template',
            nonBlocking
          );

          if (uploadResult && nonBlocking) {
            console.log('Template thumbnail processing started in background');
          }
        }
      } else {
        console.warn('Canvas element not found for template thumbnail generation');
      }
    } catch (thumbnailError) {
      // Don't fail the save operation if thumbnail generation fails
      console.error('Error generating or uploading template thumbnail:', thumbnailError);
    }
  }, 100); // Small delay to ensure the save response is returned first

  // Normalize format information and parse elements
  const { normalizeFormatData } = await import('@/utils/editor/formatUtils');
  const normalizedTemplate = normalizeFormatData(savedTemplate);

  return {
    ...normalizedTemplate,
    elements: Array.isArray(savedTemplate.elements)
      ? savedTemplate.elements
      : JSON.parse(savedTemplate.elements || '[]'),
  };
}

/**
 * Checks if the editor is in template editing mode
 * @param {Object} router - Next.js router object
 * @returns {string|null} Template ID if in template editing mode, null otherwise
 */
export function getTemplateEditingMode(router) {
  return router.query.templateId || null;
}

/**
 * Updates editor state from template data
 * @param {Object} template - The template data
 * @param {Function} setEditorState - Function to update editor state
 */
export async function initializeEditorWithTemplate(template, setEditorState) {
  if (!template) return;

  // Normalize format information to ensure consistency
  const { normalizeFormatData } = await import('@/utils/editor/formatUtils');
  const normalizedTemplate = normalizeFormatData(template);

  setEditorState(prev => ({
    ...prev,
    elements: Array.isArray(normalizedTemplate.elements) ? normalizedTemplate.elements : [],
    backgroundColor: normalizedTemplate.backgroundColor || '#ffffff',
    animationDuration: normalizedTemplate.animationDuration || 15,
    frameRate: normalizedTemplate.frameRate || 30,
    formatKey: normalizedTemplate.formatKey,
    width: normalizedTemplate.formatWidth,
    height: normalizedTemplate.formatHeight,
  }));
}