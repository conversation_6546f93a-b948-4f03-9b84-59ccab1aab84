/* Hero heading styles for mobile devices */
@media (max-width: 767px) {
  .hero-heading-mobile br {
    display: block;
  }

  .hero-heading-mobile {
    display: inline-block;
    line-height: 1.3;
  }
}

/* Ensure consistent spacing on all mobile devices */
@media (max-width: 639px) {
  .hero-heading-mobile {
    font-size: 2.25rem;
    line-height: 1.25;
  }
}

/* Desktop styles for controlled line breaks */
@media (min-width: 768px) {
  h1 span {
    display: inline-block;
    max-width: 100%;
  }

  h1 span br {
    display: block;
  }
}
