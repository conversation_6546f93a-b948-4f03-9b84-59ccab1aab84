'use client';

import { createContext, useContext, useEffect, useState } from 'react';
import { createSPASassClient } from '@/lib/supabase/client';
import { Session, User } from '@supabase/supabase-js';

// Define the AuthContextType
export interface AuthContextType {
  user: User | null;
  session: Session | null;
  isLoading: boolean;
  loading: boolean; // Alias for isLoading for backward compatibility
  error: string | null;
  signOut: () => Promise<void>;
  signInWithEmail: (email: string, password: string) => Promise<any>;
  signUpWithEmail: (email: string, password: string) => Promise<any>;
  signInWithGoogle: () => Promise<void>;
  hasPermission?: (permission: string) => boolean;
  isAdmin?: boolean;
  refreshUserProfile?: () => Promise<void>;
  supabase?: any; // For backward compatibility
}

// Create the context with the defined type
export const AuthContext = createContext<AuthContextType>({} as AuthContextType);

interface AuthProviderProps {
  children: React.ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    let mounted = true;

    const initializeAuth = async () => {
      try {
        setIsLoading(true);

        const supabaseClient = await createSPASassClient();
        const client = supabaseClient.getSupabaseClient();

        // Get the current user
        const { data: { user: authUser }, error: userError } = await client.auth.getUser();

        if (userError) {
          setError(userError.message);
          setIsLoading(false);
          return;
        }

        // Get the session
        const { data: { session: currentSession }, error: sessionError } = await client.auth.getSession();

        if (sessionError) {
          setError(sessionError.message);
          setIsLoading(false);
          return;
        }

        // Update state
        if (mounted) {
          setUser(authUser);
          setSession(currentSession);
        }

        // Set up auth state change listener
        const { data: { subscription } } = client.auth.onAuthStateChange(
          async (event, newSession) => {
            console.log(`Auth state changed: ${event}`);
            if (!mounted) return;

            setSession(newSession);
            setUser(newSession?.user ?? null);
          }
        );

        // Set loading to false
        if (mounted) setIsLoading(false);

        return () => {
          mounted = false;
          subscription.unsubscribe();
        };
      } catch (err) {
        console.error("Auth initialization error:", err);
        if (mounted) {
          setError(err instanceof Error ? err.message : 'An unknown error occurred');
          setIsLoading(false);
        }
      }
    };

    initializeAuth();
  }, []);

  const signOut = async () => {
    try {
      const supabaseClient = await createSPASassClient();
      await supabaseClient.logout();
    } catch (err) {
      console.error('Error signing out:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    }
  };

  const signInWithEmail = async (email: string, password: string) => {
    try {
      const supabaseClient = await createSPASassClient();
      const supabase = supabaseClient.getSupabaseClient();

      // Call the Supabase auth API
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      });

      // If there's an error, throw it so it can be caught by the login page
      if (error) {
        console.error('Supabase auth error:', error);
        throw new Error(error.message);
      }

      return data;
    } catch (err) {
      console.error('Error signing in with email:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      throw err;
    }
  };

  const signUpWithEmail = async (email: string, password: string) => {
    try {
      const supabaseClient = await createSPASassClient();
      const supabase = supabaseClient.getSupabaseClient();

      // Sign up the user
      const result = await supabase.auth.signUp({
        email,
        password,
        options: {
          emailRedirectTo: `${window.location.origin}/auth/callback`
        }
      });

      // Check for specific error conditions
      if (result.error) {
        // Handle duplicate email error
        if (result.error.message.includes('already registered') ||
            result.error.message.includes('already exists')) {
          console.error('Email already registered:', email);
          result.error.message = 'An account with this email already exists. Please log in instead.';
        }

        // Handle other errors
        setError(result.error.message);
        return result;
      }

      // Check if email confirmation was sent successfully
      if (!result.data.user?.identities || result.data.user.identities.length === 0) {
        console.error('Email confirmation failed to send:', result.data.user);

        // Instead of modifying result.error directly, create a new object with the error message
        const errorMessage = 'Registration successful, but the confirmation email failed to send. Please contact support.';
        setError(errorMessage);

        // Return a modified copy of the result with our custom error
        return {
          data: { user: null, session: null },
          error: {
            message: errorMessage,
            name: 'EmailSendError',
            status: 500,
            __isAuthError: true,
            code: 'email_send_failed'
          }
        };
      }

      // If signup was successful, immediately sign out to prevent auto-login
      if (result.data.user && !result.error) {
        console.log('Registration successful, signing out to prevent auto-login');
        await supabase.auth.signOut();
      }

      return result;
    } catch (err) {
      console.error('Error signing up with email:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      throw err;
    }
  };

  const signInWithGoogle = async () => {
    try {
      const supabaseClient = await createSPASassClient();
      const supabase = supabaseClient.getSupabaseClient();
      await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}/auth/callback`
        }
      });
    } catch (err) {
      console.error('Error signing in with Google:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      throw err;
    }
  };

  // Check if user has admin role
  const isAdmin = user?.app_metadata?.role === 'admin' || user?.user_metadata?.role === 'admin';

  // Function to check if user has a specific permission
  const hasPermission = (permission: string): boolean => {
    if (isAdmin) return true;
    const userPermissions = user?.user_metadata?.permissions || [];
    return userPermissions.includes(permission) || userPermissions.includes('*');
  };

  // Function to refresh user profile
  const refreshUserProfile = async (): Promise<void> => {
    try {
      const supabaseClient = await createSPASassClient();
      const supabase = supabaseClient.getSupabaseClient();
      const { data: { user: refreshedUser }, error: refreshError } = await supabase.auth.getUser();

      if (refreshError) {
        setError(refreshError.message);
        return;
      }

      setUser(refreshedUser);
    } catch (err) {
      console.error('Error refreshing user profile:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    }
  };

  const value = {
    user,
    session,
    isLoading,
    loading: isLoading, // Alias for backward compatibility
    error,
    signOut,
    signInWithEmail,
    signUpWithEmail,
    signInWithGoogle,
    hasPermission,
    isAdmin,
    refreshUserProfile,
    supabase: null // Will be set by client components if needed
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export const useAuth = () => useContext(AuthContext);
