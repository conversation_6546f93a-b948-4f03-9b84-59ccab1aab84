"use client";

export const dynamic = 'force-dynamic';

import React from 'react';
import { useState, useEffect } from 'react';
import useSWR from 'swr';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { useAuth } from '@/contexts/AuthContext';
import Navbar from '@/components/dashboard/Navbar';
import { fetcher } from '@/utils/common/fetcher';

const TemplateCard = ({ template, onPublishToggle, onDelete, onEdit }) => {
  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <li className="p-4 hover:bg-gray-50 transition-colors">
      <div className="flex flex-col md:flex-row md:items-center justify-between space-y-4 md:space-y-0">
        <div className="flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-4">
          {/* Template Preview */}
          <div className="w-full md:w-32 relative bg-gray-200 rounded overflow-hidden">
            <div
              className="w-full relative"
              style={{
                paddingBottom: `${(template.formatHeight / template.formatWidth) * 100}%`,
                minHeight: '60px',
                maxHeight: '120px'
              }}
            >
              {template.thumbnail ? (
                <div className="absolute inset-0 flex items-center justify-center p-1">
                  <Image
                    src={template.thumbnail}
                    alt={template.name}
                    fill
                    sizes="(max-width: 768px) 100vw, 128px"
                    className="object-contain rounded"
                    style={{ maxHeight: '100%', maxWidth: '100%' }}
                  />
                </div>
              ) : (
                <div className="absolute inset-0 flex items-center justify-center text-gray-400 text-sm">
                  <span>{template.formatWidth}x{template.formatHeight}</span>
                </div>
              )}
            </div>
          </div>

          {/* Template Info */}
          <div className="space-y-2">
            <h3 className="text-lg font-medium">{template.name}</h3>
            <div className="text-sm text-gray-500 space-y-1">
              <p>
                {template.formatWidth}x{template.formatHeight} • {template.formatKey}
              </p>
              <p>
                {template.category?.name}
                {template.subcategories?.length > 0 && (
                  <> › {template.subcategories.map(sub => sub.name).join(', ')}</>
                )}
              </p>
              {template.tags?.length > 0 && (
                <div className="flex flex-wrap gap-1">
                  {template.tags.map(tag => (
                    <span
                      key={tag.id}
                      className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800"
                    >
                      {tag.name}
                    </span>
                  ))}
                </div>
              )}
              <p className="text-xs text-gray-400">
                Version {template.version} • Created {formatDate(template.createdAt)}
              </p>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex flex-wrap items-center gap-2">
          <span className={`px-2 py-1 text-xs rounded ${
            template.isPublished ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
          }`}>
            {template.isPublished ? 'Published' : 'Draft'}
          </span>

          <button
            onClick={() => onPublishToggle(template)}
            className="flex-1 md:flex-none text-sm px-3 py-1 rounded border hover:bg-gray-50"
          >
            {template.isPublished ? 'Unpublish' : 'Publish'}
          </button>

          <button
            onClick={() => onEdit(template.id)}
            className="flex-1 md:flex-none text-sm px-3 py-1 rounded border hover:bg-gray-50"
          >
            Edit
          </button>

          <button
            onClick={() => onDelete(template.id)}
            className="flex-1 md:flex-none text-sm px-3 py-1 rounded border border-red-200 text-red-600 hover:bg-red-50"
          >
            Delete
          </button>
        </div>
      </div>
    </li>
  );
};

const AdminTemplatesPage = () => {
  const { user, isLoading: authLoading, error: authError } = useAuth();
  const router = useRouter();
  const { data, error: templatesError, mutate: mutateTemplates } = useSWR('/api/templates', fetcher);
  const { data: categoriesData } = useSWR('/api/template-categories', fetcher);

  // Memoize the derived data
  const templates = React.useMemo(() => data || [], [data]);
  const categories = React.useMemo(() => categoriesData || [], [categoriesData]);

  // Debug logging
  useEffect(() => {
    console.log('Templates data:', data);
    console.log('Categories data:', categoriesData);
    console.log('Processed categories:', categories);
  }, [data, categoriesData, categories]);

  const [selectedCategoryId, setSelectedCategoryId] = useState('all');
  const [selectedSubcategoryId, setSelectedSubcategoryId] = useState('all');
  const [subcategories, setSubcategories] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoadingSubcategories, setIsLoadingSubcategories] = useState(false);

  // Fetch subcategories when category changes
  useEffect(() => {
    // Skip effect if categories aren't loaded yet
    if (!categoriesData) return;

    if (selectedCategoryId === 'all') {
      setSubcategories([]);
      return;
    }

    let isMounted = true;
    setIsLoadingSubcategories(true);

    // Fetch subcategories for the selected category
    const fetchSubcategories = async () => {
      try {
        console.log('Fetching subcategories for category:', selectedCategoryId);
        const response = await fetch(`/api/template-subcategories?categoryId=${selectedCategoryId}`, {
          credentials: 'include',
          cache: 'no-store' // Prevent caching
        });

        if (!isMounted) return;

        if (!response.ok) {
          throw new Error('Failed to fetch subcategories');
        }

        const data = await response.json();
        console.log('Fetched subcategories:', data);

        if (!isMounted) return;

        // Only update if subcategories have actually changed
        if (JSON.stringify(data) !== JSON.stringify(subcategories)) {
          setSubcategories(data);
          setSelectedSubcategoryId('all');
        }
      } catch (error) {
        console.error('Error fetching subcategories:', error);
      } finally {
        if (isMounted) {
          setIsLoadingSubcategories(false);
        }
      }
    };

    fetchSubcategories();

    // Cleanup function to prevent state updates after unmount
    return () => {
      isMounted = false;
    };
  }, [selectedCategoryId, categoriesData]);

  // Permission checks are now handled by the AdminLayout component

  const handlePublishToggle = async (template) => {
    try {
      const response = await fetch(`/api/templates/${template.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify({
          isPublished: !template.isPublished
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.details || error.error || 'Failed to toggle template status');
      }

      await mutateTemplates();
    } catch (error) {
      console.error('Failed to toggle template publish status:', error);
      alert(error.message);
    }
  };

  const handleDelete = async (templateId) => {
    if (!confirm('Are you sure you want to delete this template?')) return;

    try {
      const response = await fetch(`/api/templates/${templateId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include', // This is sufficient to send cookies
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.details || error.error || 'Failed to delete template');
      }

      await mutateTemplates();
    } catch (error) {
      console.error('Failed to delete template:', error);
      alert(error.message);
    }
  };

  if (authLoading) {
    return (
      <div className="min-h-screen bg-gray-100">
        <div className="flex items-center justify-center h-screen">
          <div className="text-center">
            <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-600 mx-auto"></div>
            <div className="text-lg mt-4">Loading...</div>
          </div>
        </div>
      </div>
    );
  }

  if (authError) {
    return (
      <div className="min-h-screen bg-gray-100">
        <div className="flex items-center justify-center h-screen">
          <div className="text-lg text-red-600">Authentication error: {authError}</div>
        </div>
      </div>
    );
  }

  if (!user || user.role !== 'admin') {
    return (
      <div className="min-h-screen bg-gray-100">
        <Navbar user={user} />

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex flex-col space-y-6">
            <div className="flex flex-col md:flex-row md:items-center justify-between space-y-4 md:space-y-0">
              <h1 className="text-2xl font-bold">Template Management</h1>
            </div>

            <div className="text-lg text-red-600">
              Access Denied: You don't have permission to manage templates
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (templatesError) {
    return (
      <div className="min-h-screen bg-gray-100">
        <Navbar user={user} />

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex flex-col space-y-6">
            <div className="flex flex-col md:flex-row md:items-center justify-between space-y-4 md:space-y-0">
              <h1 className="text-2xl font-bold">Template Management</h1>
            </div>

            <div className="text-lg text-red-600">
              Error loading templates: {templatesError.message}
              <button
                onClick={() => window.location.reload()}
                className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
              >
                Retry
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Filter templates based on category, subcategory, and search query
  const filteredTemplates = templates?.filter(template => {
    if (selectedCategoryId !== 'all' && template.categoryId !== selectedCategoryId) return false;
    if (selectedSubcategoryId !== 'all' && !template.subcategories?.some(sub => sub.id === selectedSubcategoryId)) return false;
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      return (
        template.name.toLowerCase().includes(query) ||
        template.description?.toLowerCase().includes(query) ||
        template.category?.name.toLowerCase().includes(query) ||
        template.subcategories?.some(sub => sub.name.toLowerCase().includes(query)) ||
        template.tags?.some(tag => tag.name.toLowerCase().includes(query))
      );
    }
    return true;
  });

  return (
    <div className="min-h-screen bg-gray-100">
      <Navbar user={user} />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col space-y-6">
          <div className="flex flex-col md:flex-row md:items-center justify-between space-y-4 md:space-y-0">
            <h1 className="text-2xl font-bold">Template Management</h1>
            <button
              onClick={() => router.push('/admin/templates/new')}
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Create New Template
            </button>
          </div>

      {/* Filters */}
      <div className="flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4">
        <input
          type="text"
          placeholder="Search templates..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="flex-1 md:max-w-xs px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
        />

        <select
          value={selectedCategoryId}
          onChange={(e) => setSelectedCategoryId(e.target.value)}
          className="flex-1 md:max-w-xs px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="all">All Categories</option>
          {categories.map(category => (
            <option key={category.id} value={category.id}>
              {category.name}
            </option>
          ))}
        </select>

        {subcategories.length > 0 && (
          <select
            value={selectedSubcategoryId}
            onChange={(e) => setSelectedSubcategoryId(e.target.value)}
            className="flex-1 md:max-w-xs px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="all">All Subcategories</option>
            {subcategories.map(subcategory => (
              <option key={subcategory.id} value={subcategory.id}>
                {subcategory.name}
              </option>
            ))}
          </select>
        )}
      </div>

      {/* Templates List */}
      {!data ? (
        <div className="text-center py-12">
          <div className="text-lg">Loading templates...</div>
        </div>
      ) : filteredTemplates.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-lg text-gray-500">No templates found</div>
        </div>
      ) : (
        <ul className="bg-white shadow rounded-lg divide-y divide-gray-200">
          {filteredTemplates.map(template => (
            <TemplateCard
              key={template.id}
              template={template}
              onPublishToggle={handlePublishToggle}
              onDelete={handleDelete}
              onEdit={(id) => router.push(`/admin/templates/${id}`)}
            />
          ))}
        </ul>
      )}
        </div>
      </div>
    </div>
  );
};

export default AdminTemplatesPage;