"use client";

import React, { useState, useEffect, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { createSPAClient } from '@/lib/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import Navbar from '@/components/dashboard/Navbar';
import OverviewSection from '@/components/dashboard/OverviewSection';
import ProjectsSection from '@/components/dashboard/ProjectsSection';
import BannersSection from '@/components/dashboard/BannersSection';
import DeleteProjectModal from '@/components/dashboard/DeleteProjectModal';
import ProjectSettingsModal from '@/components/dashboard/ProjectSettingsModal';
import { hasReachedProjectLimit, hasReachedBannerLimit } from '@/lib/subscription/limits';


const AUTH_TIMEOUT = 15000; // 15 seconds

/**
 * Simplified Dashboard Page
 * Main entry point for the dashboard without complex state management
 */
export default function Dashboard() {
  // Get auth state from AuthContext
  const { user, isLoading: isAuthLoading } = useAuth();
  const router = useRouter();
  const [authTimeout, setAuthTimeout] = useState(false);
  const [authInitialized, setAuthInitialized] = useState(false);

  useEffect(() => {
    if (isAuthLoading) return;

    console.log('Access check:', {
      authLoading: isAuthLoading,
    });

    // Mark auth as initialized once loading is complete
    if (!isAuthLoading) {
      setAuthInitialized(true);
    }
  }, [isAuthLoading, router, user]);

  useEffect(() => {
    const timer = setTimeout(() => {
      if (!user && isAuthLoading) {
        setAuthTimeout(true);
      }
    }, AUTH_TIMEOUT);

    return () => clearTimeout(timer);
  }, [user, isAuthLoading]);

  // Show loading screen while authenticating
  if (!user && isAuthLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-foreground mb-4 mx-auto"></div>
          <p className="text-foreground">
            {authTimeout ?
              "Taking longer than usual? Try refreshing the page 😊." :
              "Verifying access..."}
          </p>
        </div>
      </div>
    );
  }


  // State
  const [projects, setProjects] = useState<any[]>([]);
  const [selectedProject, setSelectedProject] = useState<any | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Project editing states
  const [isCreatingProject, setIsCreatingProject] = useState<boolean>(false);
  const [isCreatingBanner, setIsCreatingBanner] = useState<boolean>(false);
  const [deletingBannerId, setDeletingBannerId] = useState<string | null>(null);
  const [editingProjectId, setEditingProjectId] = useState<string | null>(null);
  const [editingTitle, setEditingTitle] = useState<string>('');

  // Pagination states
  interface PaginationState {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasMore: boolean;
    isLoading: boolean;
  }

  const [projectsPagination, setProjectsPagination] = useState<PaginationState>({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 1,
    hasMore: false,
    isLoading: false
  });

  const [bannersPagination, setBannersPagination] = useState<PaginationState>({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 1,
    hasMore: false,
    isLoading: false
  });

  // Modal states
  const [showDeleteModal, setShowDeleteModal] = useState<boolean>(false);
  const [showSettingsModal, setShowSettingsModal] = useState<boolean>(false);
  const [projectToDelete, setProjectToDelete] = useState<any | null>(null);
  const [projectToEdit, setProjectToEdit] = useState<any | null>(null);

  interface ProjectSettings {
    title: string;
    description: string;
    status: string;
  }

  const [editingSettings, setEditingSettings] = useState<ProjectSettings>({
    title: '',
    description: '',
    status: 'active'
  });
  const [isDeleting, setIsDeleting] = useState<boolean>(false);
  const [deleteError, setDeleteError] = useState<string>('');

  // Initialize Supabase client with useMemo to create a stable instance
  // Using 'any' type to avoid excessive type instantiation issues
  const supabase = useMemo(() => createSPAClient() as any, []);

  // Fetch projects when user is authenticated - only once
  useEffect(() => {
    // Skip if auth is still loading
    if (isAuthLoading) {
      return;
    }

    // If auth is initialized but no user, redirect to login
    if (!isAuthLoading && authInitialized && !user) {
      window.location.href = '/login';
      return;
    }

    // If we have a user, fetch their projects - but only once
    if (user && !projects.length) {
      const loadProjects = async () => {
        try {
          setIsLoading(true);
          // Initial load doesn't need force refresh since projects.length is 0
          await fetchProjects(user.id, false);
        } catch (error) {
          console.error('Error loading projects:', error);
          setError('Failed to load projects. Please try again.');
        } finally {
          setIsLoading(false);
        }
      };

      loadProjects();
    }
  }, [user, isAuthLoading, authInitialized, projects.length]);

  // Fetch projects for a user - with caching to prevent redundant calls
  async function fetchProjects(userId: string, forceRefresh: boolean = false) {
    try {
      // Check if we already have projects loaded and avoid redundant fetching
      // Skip this check if forceRefresh is true
      if (!forceRefresh && projects.length > 0 && !isLoading) {
        console.log('Projects already loaded, skipping fetch');
        return;
      }

      console.log('Fetching projects for user:', userId);
      setProjectsPagination(prev => ({ ...prev, isLoading: true }));

      // Define explicit types for the query result
      interface ProjectWithBanners {
        id: string;
        title: string;
        description?: string;
        userId: string;
        createdAt: string;
        updatedAt: string;
        status?: string;
        banners: any[];
      }

      const result = await supabase
        .from('projects')
        .select('*, banners(*)', { count: 'exact' })
        .eq('userId', userId)
        .order('createdAt', { ascending: false });

      const data = result.data as ProjectWithBanners[] | null;
      const error = result.error;
      const count = result.count;

      if (error) {
        throw error;
      }

      // Only update state if we have new data
      if (data) {
        console.log(`Loaded ${data.length} projects`);
        setProjects(data);

        // Update pagination
        setProjectsPagination(prev => ({
          ...prev,
          total: count || 0,
          totalPages: Math.ceil((count || 0) / prev.limit),
          hasMore: (count || 0) > prev.limit * prev.page,
          isLoading: false
        }));

        // Select the first project by default if available and no project is currently selected
        if (data.length > 0 && !selectedProject) {
          setSelectedProject(data[0]);

          // Update banners pagination if we have a selected project
          if (data[0].banners) {
            const bannerCount = data[0].banners.length;
            setBannersPagination(prev => ({
              ...prev,
              total: bannerCount,
              totalPages: Math.ceil(bannerCount / prev.limit),
              hasMore: bannerCount > prev.limit * prev.page,
              isLoading: false
            }));
          }
        }
      } else {
        // If no data, just update loading state
        setProjectsPagination(prev => ({ ...prev, isLoading: false }));
      }
    } catch (error) {
      console.error('Error fetching projects:', error);
      setError('Failed to load projects. Please try again.');
      setProjectsPagination(prev => ({ ...prev, isLoading: false }));
    }
  }

  // Create a new project
  async function handleCreateProject() {
    try {
      if (!user) return;

      setIsCreatingProject(true);

      // Check subscription limits before creating a project
      // Get user profile to check subscription tier
      const { data: userProfile } = await supabase
        .from('profiles')
        .select('subscriptionTier')
        .eq('id', user.id)
        .single();

      const subscriptionTier = userProfile?.subscriptionTier || 'FREE';
      const { hasReachedLimit, currentCount, limit } = await hasReachedProjectLimit(
        supabase,
        user.id,
        subscriptionTier
      );

      if (hasReachedLimit) {
        console.log(`User has reached project limit: ${currentCount}/${limit}`);
        throw new Error(`You have reached your project limit (${currentCount}/${limit}). Please upgrade your subscription to create more projects.`);
      }

      // First, check if the user has a profile
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('id')
        .eq('id', user.id)
        .maybeSingle(); // Use maybeSingle instead of single to avoid error when not found

      // If there's an error that's not just "not found", handle it
      if (profileError && profileError.code !== 'PGRST116') {
        console.error('Error checking user profile:', profileError);
        throw new Error('Could not verify user profile. Please try again later.');
      }

      // If profile doesn't exist or there was a "not found" error, create a profile
      if (!profileData) {
        console.log('User profile not found. Creating profile first.');

        try {
          // Create a profile for the user if it doesn't exist
          const { error: createProfileError } = await supabase
            .from('profiles')
            .insert([{
              id: user.id,
              email: user.email || '',
              firstName: user.user_metadata?.firstName || (user.email ? user.email.split('@')[0] : 'User'),
              lastName: user.user_metadata?.lastName || '',
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            }]);

          if (createProfileError) {
            console.error('Error creating user profile:', createProfileError);
            throw new Error('Could not create user profile. Please try again later.');
          }

          console.log('User profile created successfully');
        } catch (err) {
          console.error('Exception creating user profile:', err);
          throw new Error('Failed to create user profile. Please try again later.');
        }
      }

      // Log user information for debugging
      console.log('User information:', {
        id: user.id,
        email: user.email,
        metadata: user.user_metadata
      });

      const newProject = {
        title: 'New Project',
        userId: user.id,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        status: 'active'
      };

      console.log('Creating new project:', newProject);

      const { data, error } = await supabase
        .from('projects')
        .insert([newProject])
        .select();

      if (error) {
        console.error('Supabase error creating project:', error);
        throw new Error(error.message || 'Failed to create project');
      }

      console.log('Project created successfully:', data);

      // Refresh projects - force refresh to ensure new project is loaded
      await fetchProjects(user.id, true);

      // Select the newly created project
      if (data && data.length > 0) {
        setSelectedProject(data[0]);
        setEditingProjectId(data[0].id);
        setEditingTitle(data[0].title);
      }
    } catch (error) {
      console.error('Error creating project:', error);
      setError(error instanceof Error ? error.message : 'Failed to create project. Please try again.');
    } finally {
      setIsCreatingProject(false);
    }
  }

  // Update project title with optimistic UI update
  async function handleUpdateProjectTitle(projectId, newTitle) {
    try {
      if (!projectId || !newTitle.trim()) return;

      // Optimistic UI update
      const updatedProjects = projects.map(project =>
        project.id === projectId
          ? { ...project, title: newTitle, updatedAt: new Date().toISOString() }
          : project
      );
      setProjects(updatedProjects);

      // If the updated project is the selected one, update that too
      if (selectedProject && selectedProject.id === projectId) {
        setSelectedProject({ ...selectedProject, title: newTitle, updatedAt: new Date().toISOString() });
      }

      // Reset editing state immediately for better UX
      setEditingProjectId(null);
      setEditingTitle('');

      // Perform the actual update in the database
      const { error } = await supabase
        .from('projects')
        .update({
          title: newTitle,
          updatedAt: new Date().toISOString()
        })
        .eq('id', projectId);

      if (error) {
        throw error;
      }

      // No need to refresh projects since we already updated the UI
    } catch (error) {
      console.error('Error updating project title:', error);
      alert('Failed to update project title. Please try again.');

      // If there was an error, refresh the data to restore the correct state
      if (user && user.id) {
        await fetchProjects(user.id, true);
      }
    }
  }

  // Delete a project
  async function handleDeleteProject(projectId) {
    try {
      if (!projectId) return;

      setIsDeleting(true);
      setDeleteError('');

      // Optimistic UI update - immediately remove the project from the UI
      const updatedProjects = projects.filter(project => project.id !== projectId);
      setProjects(updatedProjects);

      // If the deleted project was selected, select another one
      if (selectedProject && selectedProject.id === projectId) {
        // Select the first project from the updated list or null if empty
        setSelectedProject(updatedProjects.length > 0 ? updatedProjects[0] : null);
      }

      // Close the modal
      setShowDeleteModal(false);
      setProjectToDelete(null);

      // Delete the project from the database
      const { error } = await supabase
        .from('projects')
        .delete()
        .eq('id', projectId);

      if (error) {
        throw error;
      }

      // Refresh projects to ensure data consistency
      await fetchProjects(user.id, true);
    } catch (error) {
      console.error('Error deleting project:', error);
      setDeleteError('Failed to delete project. Please try again.');

      // If there was an error, refresh the data to restore the correct state
      if (user && user.id) {
        await fetchProjects(user.id, true);
      }
    } finally {
      setIsDeleting(false);
    }
  }

  // Update project settings with optimistic UI update
  async function handleUpdateProjectSettings(projectId, settings) {
    try {
      if (!projectId) return;

      // Close the modal immediately for better UX
      setShowSettingsModal(false);
      setProjectToEdit(null);

      // Optimistic UI update
      const updatedProjects = projects.map(project =>
        project.id === projectId
          ? {
              ...project,
              title: settings.title,
              description: settings.description,
              status: settings.status,
              updatedAt: new Date().toISOString()
            }
          : project
      );
      setProjects(updatedProjects);

      // If the updated project is the selected one, update that too
      if (selectedProject && selectedProject.id === projectId) {
        setSelectedProject({
          ...selectedProject,
          title: settings.title,
          description: settings.description,
          status: settings.status,
          updatedAt: new Date().toISOString()
        });
      }

      // Perform the actual update in the database
      const { error } = await supabase
        .from('projects')
        .update({
          title: settings.title,
          description: settings.description,
          status: settings.status,
          updatedAt: new Date().toISOString()
        })
        .eq('id', projectId);

      if (error) {
        throw error;
      }

      // No need to refresh projects since we already updated the UI
    } catch (error) {
      console.error('Error updating project settings:', error);
      alert('Failed to update project settings. Please try again.');

      // If there was an error, refresh the data to restore the correct state
      if (user && user.id) {
        await fetchProjects(user.id, true);
      }
    }
  }

  // Create a new banner - simplified approach
  async function handleCreateBanner(projectId) {
    try {
      if (!projectId) return;

      console.log('🚀 Starting banner creation process for project:', projectId);
      setIsCreatingBanner(true);

      // Check subscription limits before creating a banner
      // Get user profile to check subscription tier
      const { data: userProfile } = await supabase
        .from('profiles')
        .select('subscriptionTier')
        .eq('id', user.id)
        .single();

      const subscriptionTier = userProfile?.subscriptionTier || 'FREE';

      // Check if user has reached their banner limit for this project
      const { hasReachedLimit, currentCount, limit } = await hasReachedBannerLimit(
        supabase,
        user.id,
        projectId,
        subscriptionTier
      );

      if (hasReachedLimit) {
        console.log(`User has reached banner limit for project: ${currentCount}/${limit}`);
        throw new Error(`You have reached your banner limit for this project (${currentCount}/${limit}). Please upgrade your subscription to create more banners.`);
      }

      // Create the banner directly via API
      console.log('📡 Sending request to create API');
      const startTime = performance.now();

      const response = await fetch('/api/banners/create', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ projectId })
      });

      const apiTime = performance.now() - startTime;
      console.log(`📡 API request completed in ${apiTime}ms`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to create banner: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('✅ Banner created successfully:', {
        id: data.banner.id,
        userId: data.banner.userId,
        projectId: data.banner.projectId,
        performance: data.performance
      });

      // Store the banner data in sessionStorage for the editor to use
      if (typeof window !== 'undefined') {
        try {
          // Store the banner data and mark as initialized to prevent redundant API calls
          sessionStorage.setItem(`banner_${data.banner.id}`, JSON.stringify(data.banner));
          sessionStorage.setItem(`banner_${data.banner.id}_initialized`, 'true');
        } catch (e) {
          console.error('Error storing banner data in sessionStorage:', e);
        }
      }

      // Redirect to the editor with the new banner ID
      console.log('🔄 Redirecting to editor page');
      window.location.href = `/editor?projectId=${projectId}&bannerId=${data.banner.id}`;
    } catch (error) {
      console.error('❌ Error creating banner:', error);
      setError(error instanceof Error ? error.message : 'Failed to create banner. Please try again.');
      setIsCreatingBanner(false);
    }
  }

  // Delete a banner
  async function handleDeleteBanner(bannerId) {
    try {
      if (!bannerId) return;

      if (!confirm('Are you sure you want to delete this banner? This action cannot be undone.')) {
        return;
      }

      // Set the ID of the banner being deleted
      setDeletingBannerId(bannerId);

      // Optimistic UI update - immediately remove the banner from the UI
      if (selectedProject && selectedProject.banners) {
        // Create a copy of the current project with the banner removed
        const updatedBanners = selectedProject.banners.filter(banner => banner.id !== bannerId);

        // Update the selected project with the filtered banners
        setSelectedProject({
          ...selectedProject,
          banners: updatedBanners
        });

        // Update the projects list to reflect the change
        setProjects(prevProjects =>
          prevProjects.map(project =>
            project.id === selectedProject.id
              ? { ...project, banners: updatedBanners }
              : project
          )
        );

        // Update banners pagination
        const bannerCount = updatedBanners.length;
        setBannersPagination(prev => ({
          ...prev,
          total: bannerCount,
          totalPages: Math.ceil(bannerCount / prev.limit),
          hasMore: bannerCount > prev.limit * prev.page
        }));
      }

      // Perform the actual deletion in the database
      const { error } = await supabase
        .from('banners')
        .delete()
        .eq('id', bannerId);

      if (error) {
        throw error;
      }

      // Refresh projects to ensure data consistency
      await fetchProjects(user.id, true);
    } catch (error) {
      console.error('Error deleting banner:', error);
      alert('Failed to delete banner. Please try again.');

      // If there was an error, refresh the data to restore the correct state
      if (user && user.id) {
        await fetchProjects(user.id, true);
      }
    } finally {
      // Clear loading state
      setDeletingBannerId(null);
    }
  }

  // Handle project selection
  function handleSelectProject(project) {
    setSelectedProject(project);
  }

  // Handle edit project
  function handleEditProject(project) {
    setEditingProjectId(project.id);
    setEditingTitle(project.title);
  }

  // Handle open delete modal
  function handleOpenDeleteModal(project) {
    setProjectToDelete(project);
    setShowDeleteModal(true);
    setDeleteError('');
  }

  // Handle open settings modal
  function handleOpenSettingsModal(project) {
    setProjectToEdit(project);
    setEditingSettings({
      title: project.title || '',
      description: project.description || '',
      status: project.status || 'active'
    });
    setShowSettingsModal(true);
  }

  // Load more projects
  function handleLoadMoreProjects() {
    setProjectsPagination(prev => ({
      ...prev,
      page: prev.page + 1,
      isLoading: true
    }));

    // In a real implementation, you would fetch more projects here
    // For now, we'll just simulate it by updating the pagination state
    setTimeout(() => {
      setProjectsPagination(prev => ({
        ...prev,
        isLoading: false
      }));
    }, 500);
  }

  // Load more banners
  function handleLoadMoreBanners() {
    setBannersPagination(prev => ({
      ...prev,
      page: prev.page + 1,
      isLoading: true
    }));

    // In a real implementation, you would fetch more banners here
    // For now, we'll just simulate it by updating the pagination state
    setTimeout(() => {
      setBannersPagination(prev => ({
        ...prev,
        isLoading: false
      }));
    }, 500);
  }

  // Show error state if there's an error
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="bg-white shadow-md rounded-lg p-6 max-w-md w-full">
          <div className="text-red-500 mb-4 flex justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-center mb-2">Something went wrong</h2>
          <p className="text-gray-600 text-center mb-6">{error}</p>
          <div className="flex justify-center">
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              Reload Dashboard
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Main dashboard render
  return (
    <div className="min-h-screen bg-gray-100">
      <Navbar user={user} />

      <main className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <OverviewSection projects={projects} />

        <ProjectsSection
          projects={projects}
          selectedProject={selectedProject}
          onCreateProject={handleCreateProject}
          onSelectProject={handleSelectProject}
          editingProjectId={editingProjectId}
          editingTitle={editingTitle}
          setEditingTitle={setEditingTitle}
          onUpdateProjectTitle={handleUpdateProjectTitle}
          onEditProject={handleEditProject}
          onOpenDeleteModal={handleOpenDeleteModal}
          onOpenSettingsModal={handleOpenSettingsModal}
          isCreatingProject={isCreatingProject}
          isLoading={false}
          pagination={projectsPagination}
          onLoadMore={handleLoadMoreProjects}
        />

        {selectedProject && (
          <BannersSection
            project={selectedProject}
            onCreateBanner={() => handleCreateBanner(selectedProject.id)}
            onBannerDelete={handleDeleteBanner}
            isCreatingBanner={isCreatingBanner}
            deletingBannerId={deletingBannerId}
            pagination={bannersPagination}
            onLoadMore={handleLoadMoreBanners}
          />
        )}

        {showDeleteModal && (
          <DeleteProjectModal
            show={showDeleteModal}
            project={projectToDelete}
            onClose={() => {
              setShowDeleteModal(false);
              setProjectToDelete(null);
              setDeleteError('');
              setIsDeleting(false);
            }}
            onDelete={() => handleDeleteProject(projectToDelete?.id)}
            error={deleteError}
            isDeleting={isDeleting}
          />
        )}

        {showSettingsModal && (
          <ProjectSettingsModal
            show={showSettingsModal}
            settings={editingSettings}
            onSettingsChange={setEditingSettings}
            onClose={() => {
              setShowSettingsModal(false);
              setProjectToEdit(null);
            }}
            onSave={() => handleUpdateProjectSettings(projectToEdit?.id, editingSettings)}
          />
        )}
      </main>
    </div>
  );
}
