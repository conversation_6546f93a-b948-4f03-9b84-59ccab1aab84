'use client';

export const dynamic = 'force-dynamic';

import React from 'react';
import { useEffect, useState, useCallback } from 'react';

import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { useCustomToast } from '@/hooks/useCustomToast';
import Navbar from '@/components/dashboard/Navbar';
import { createSPAClient } from '@/lib/supabase/client';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  HiOutlineRefresh,
  HiCheckCircle,
  HiXCircle,
  HiExclamationCircle,
  HiCheck
} from 'react-icons/hi';
import { Loader2 } from 'lucide-react';
import { SUBSCRIPTION_TIERS } from '@/lib/subscription/limits';

// Define subscription tiers with pricing information
// This extends the core subscription tiers defined in @/lib/subscription/limits
const SUBSCRIPTION_PRICING = {
  FREE: {
    name: 'Free',
    monthly: {
      price: 0,
      savePercent: 0
    },
    annual: {
      price: 0,
      savePercent: 0
    },
    features: [
      '2 banners only',
      'All animations',
      'HTML5 export',
      'No AI features',
    ],
    limits: {
      projects: 1,
      bannersPerProject: 2
    }
  },
  PRO: {
    name: 'Pro',
    monthly: {
      price: 25,
      savePercent: 0
    },
    annual: {
      price: 170,
      savePercent: 30
    },
    features: [
      'All Basic plan features',
      'Unlimited banners',
      // 'Template library access',
      'AI Studio access',
    ],
    limits: {
      projects: Infinity,
      bannersPerProject: Infinity
    }
  },
  BUSINESS: {
    name: 'Business',
    monthly: {
      price: 39,
      savePercent: 0
    },
    annual: {
      price: 270, // 27 * 10 = 270 to match homepage
      savePercent: 30
    },
    features: [
      'All Pro plan features',
      'Custom template request',
      'Priority support',
      // 'Dedicated support',
      // 'SSO authentication'
    ],
    limits: {
      projects: Infinity,
      bannersPerProject: Infinity,
      teamMembers: 1
    }
  }
};

export default function Subscription() {
  const { user: authUser, isLoading: authLoading } = useAuth();

  // Define a custom user type that includes subscription information
  interface UserWithSubscription {
    id: string;
    email: string;
    subscriptionTier?: string;
    [key: string]: any;
  }

  const [user, setUser] = useState<UserWithSubscription | null>(null);
  const [loading, setLoading] = useState(true);
  const [processingAction, setProcessingAction] = useState(false);
  const { showToast } = useCustomToast();
  const [showCancelModal, setShowCancelModal] = useState<boolean>(false);

  interface SubscriptionStatus {
    status?: string;
    statusFormatted?: string;
    renewsAt?: string;
    endsAt?: string;
    canChangePlan?: boolean;
    canCancel?: boolean;
    canResume?: boolean;
    urls?: {
      update_payment_method?: string;
    };
    [key: string]: any;
  }

  const [subscriptionStatus, setSubscriptionStatus] = useState<SubscriptionStatus | null>(null);
  const [formattedNextBillingDate, setFormattedNextBillingDate] = useState<string | null>(null);
  const [formattedEndDate, setFormattedEndDate] = useState<string | null>(null);
  const [billingInterval, setBillingInterval] = useState<'monthly' | 'annual'>('monthly');

  // Simple subscription status loading
  const loadSubscriptionStatus = useCallback(async () => {
    if (!authUser) return;

    // Set user state from authUser
    const userObj = {
      id: authUser.id,
      email: authUser.email || '',
      subscriptionTier: 'FREE' // Default to FREE
    };

    setUser(userObj);

    // Default subscription status for free tier
    const freeStatus = {
      subscriptionTier: 'FREE',
      status: null,
      renewsAt: null,
      endsAt: null,
      canResume: false,
      canCancel: false,
      canChangePlan: false,
    };

    // Fetch the user's profile to get the subscription tier
    try {
      const supabase = createSPAClient();
      const { data: profile } = await supabase
        .from('profiles')
        .select('subscriptionTier')
        .eq('id', authUser.id)
        .single();

      if (profile?.subscriptionTier) {
        const updatedUserObj = {
          ...userObj,
          subscriptionTier: profile.subscriptionTier
        };
        setUser(updatedUserObj);

        // If not free tier, continue to fetch subscription status
        if (profile.subscriptionTier !== 'FREE') {
          // Continue to API call below
        } else {
          // If free tier, no need for API call
          setSubscriptionStatus(freeStatus);
          setLoading(false);
          return;
        }
      } else {
        // If no subscription tier found, assume FREE
        setSubscriptionStatus(freeStatus);
        setLoading(false);
        return;
      }
    } catch (err) {
      console.error('Error fetching user profile:', err);
      // If error, assume FREE
      setSubscriptionStatus(freeStatus);
      setLoading(false);
      return;
    }

    try {
      const res = await fetch('/api/subscription/status');
      const data = await res.json();

      if (data.error) {
        throw new Error(data.error);
      }

      setSubscriptionStatus(data);

      // Format dates
      if (data.renewsAt) {
        setFormattedNextBillingDate(new Date(data.renewsAt).toLocaleDateString());
      }
      if (data.endsAt) {
        setFormattedEndDate(new Date(data.endsAt).toLocaleDateString());
      }
    } catch (error) {
      console.error('Error loading subscription:', error);
      setSubscriptionStatus({
        subscriptionTier: (authUser as any).subscriptionTier || 'FREE',
        status: 'unknown',
        canResume: false,
        canCancel: false,
        canChangePlan: false,
      });
    } finally {
      setLoading(false);
    }
  }, [authUser]);

  // Load subscription data once when component mounts
  useEffect(() => {
    loadSubscriptionStatus();
  }, [loadSubscriptionStatus]);

  const handleUpgrade = async (tier: string) => {
    // FREE tier is no longer shown in the UI, but we'll keep this logic for completeness
    if (tier === 'FREE') {
      // For downgrading to FREE, we need to cancel the subscription
      setShowCancelModal(true);
      return;
    }

    if (tier === user?.subscriptionTier) {
      return;
    }

    setProcessingAction(true);

    try {
      // If user already has a subscription, try to change the plan
      if (subscriptionStatus?.canChangePlan) {
        const res = await fetch('/api/subscription/change-plan', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            tier,
            billingInterval
          })
        });

        if (!res.ok) {
          throw new Error('Failed to change subscription plan');
        }

        const data = await res.json();

        // Check if the plan change requires payment
        if (data.requiresPayment && data.checkoutUrl) {
          // If payment is required, redirect to the checkout URL
          console.log('Plan change requires payment. Redirecting to checkout:', data.checkoutUrl);
          window.location.href = data.checkoutUrl;
          return;
        } else if (data.success) {
          // If no payment is required and the change was successful
          showToast({
            type: 'success',
            title: 'Success',
            message: 'Subscription plan changed successfully'
          });

          // Reload the page to refresh data
          window.location.reload();
        }
      } else {
        // If user doesn't have an active subscription, create a new checkout
        const res = await fetch('/api/subscription/checkout', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            tier,
            billingInterval
          })
        });

        if (!res.ok) {
          throw new Error('Failed to create checkout');
        }

        const { checkoutUrl } = await res.json();
        console.log('Redirecting to checkout URL:', checkoutUrl);
        window.location.href = checkoutUrl;
      }
    } catch (error) {
      console.error('Error upgrading subscription:', error);
      showToast({
        type: 'error',
        title: 'Error',
        message: 'Failed to upgrade subscription'
      });
    } finally {
      setProcessingAction(false);
    }
  };

  const toggleBillingInterval = () => {
    setBillingInterval(billingInterval === 'monthly' ? 'annual' : 'monthly');
  };

  const handleCancelSubscription = async () => {
    setProcessingAction(true);

    try {
      const res = await fetch('/api/subscription/cancel', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      if (!res.ok) {
        throw new Error('Failed to cancel subscription');
      }

      showToast({
        type: 'success',
        title: 'Success',
        message: 'Subscription cancelled successfully'
      });

      // Close modal and reload page
      setShowCancelModal(false);
      window.location.reload();
    } catch (error) {
      console.error('Error cancelling subscription:', error);
      showToast({
        type: 'error',
        title: 'Error',
        message: 'Failed to cancel subscription'
      });
    } finally {
      setProcessingAction(false);
    }
  };

  const handleResumeSubscription = async () => {
    setProcessingAction(true);

    try {
      const res = await fetch('/api/subscription/resume', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      if (!res.ok) {
        throw new Error('Failed to resume subscription');
      }

      showToast({
        type: 'success',
        title: 'Success',
        message: 'Subscription resumed successfully'
      });

      // Reload page to refresh data
      window.location.reload();
    } catch (error) {
      console.error('Error resuming subscription:', error);
      showToast({
        type: 'error',
        title: 'Error',
        message: 'Failed to resume subscription'
      });
    } finally {
      setProcessingAction(false);
    }
  };

  const handleUpdatePaymentMethod = () => {
    if (subscriptionStatus?.urls?.update_payment_method) {
      window.open(subscriptionStatus.urls.update_payment_method, '_blank');
    }
  };

  const getStatusIcon = (status: string | undefined) => {
    if (!status) return null;

    switch (status) {
      case 'active':
        return <HiCheckCircle className="h-5 w-5 text-green-500" />;
      case 'cancelled':
        return <HiXCircle className="h-5 w-5 text-orange-500" />;
      case 'expired':
        return <HiXCircle className="h-5 w-5 text-red-500" />;
      case 'past_due':
        return <HiExclamationCircle className="h-5 w-5 text-red-500" />;
      default:
        return null;
    }
  };

  // No timeout needed

  if (loading || authLoading) {
    // Simple loading screen without steps


    return (
      <div className="min-h-screen flex flex-col bg-gradient-to-br from-blue-50 to-gray-50">
        <Navbar />
        <div className="flex-grow flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <h2 className="text-xl font-semibold mb-2">Loading Subscription Details</h2>
            <p className="text-gray-600">Please wait...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-gray-50">
      <Navbar />
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="flex flex-col space-y-8">

          {/* Current Subscription Status */}
          <div className="bg-white rounded-xl shadow-md p-8 border border-blue-100">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-6">
              <div>
                <h2 className="text-2xl font-bold text-gray-800 mb-3">Current Subscription</h2>
                <div className="flex items-center gap-2 mb-2">
                  <p className="text-gray-700">
                    You are currently on the{' '}
                    <span className="font-bold text-blue-600">{SUBSCRIPTION_PRICING[user?.subscriptionTier || 'FREE']?.name || 'Free'}</span> plan
                  </p>
                  {getStatusIcon(subscriptionStatus?.status)}
                  {subscriptionStatus?.statusFormatted && (
                    <span className="text-sm font-medium text-gray-500">({subscriptionStatus.statusFormatted})</span>
                  )}
                </div>

                {formattedNextBillingDate && (
                  <div className="flex items-center gap-2 text-sm text-gray-600 mt-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <span>Next billing date: <span className="font-medium">{formattedNextBillingDate}</span></span>
                  </div>
                )}

                {formattedEndDate && (
                  <div className="flex items-center gap-2 text-sm text-gray-600 mt-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-orange-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span>Subscription ends on: <span className="font-medium">{formattedEndDate}</span></span>
                  </div>
                )}

                {/* Current Plan Limits */}
                <div className="mt-4 border-t pt-4">
                  <h3 className="text-sm font-semibold text-gray-700 mb-2">Your Current Plan Limits:</h3>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                    {user?.subscriptionTier && (
                      <>
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z" />
                          </svg>
                          <span>
                            Projects: <span className="font-medium">
                              {SUBSCRIPTION_TIERS[user.subscriptionTier]?.limits.projects === Infinity
                                ? 'Unlimited'
                                : SUBSCRIPTION_TIERS[user.subscriptionTier]?.limits.projects}
                            </span>
                          </span>
                        </div>
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6z" />
                          </svg>
                          <span>
                            Banners per project: <span className="font-medium">
                              {SUBSCRIPTION_TIERS[user.subscriptionTier]?.limits.bannersPerProject === Infinity
                                ? 'Unlimited'
                                : SUBSCRIPTION_TIERS[user.subscriptionTier]?.limits.bannersPerProject}
                            </span>
                          </span>
                        </div>
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                          </svg>
                          <span>
                            Custom formats: <span className="font-medium">
                              {SUBSCRIPTION_TIERS[user.subscriptionTier]?.limits.customFormats === Infinity
                                ? 'Unlimited'
                                : SUBSCRIPTION_TIERS[user.subscriptionTier]?.limits.customFormats}
                            </span>
                          </span>
                        </div>
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                          </svg>
                          <span>
                            AI Generation: <span className="font-medium">
                              {SUBSCRIPTION_TIERS[user.subscriptionTier]?.limits.aiGeneration ? 'Yes' : 'No'}
                            </span>
                          </span>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </div>

              <div className="flex flex-wrap gap-3">
                {subscriptionStatus?.canCancel && (
                  <Button
                    variant="outline"
                    onClick={() => setShowCancelModal(true)}
                    disabled={processingAction}
                    className="border-gray-300 hover:bg-gray-50 hover:text-gray-900 transition-all"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                    Cancel Subscription
                  </Button>
                )}

                {subscriptionStatus?.canResume && (
                  <Button
                    variant="default"
                    onClick={handleResumeSubscription}
                    disabled={processingAction}
                    className="bg-blue-600 hover:bg-blue-700 text-white"
                  >
                    {processingAction ? (
                      <>
                        <HiOutlineRefresh className="mr-2 h-4 w-4 animate-spin" />
                        Resuming...
                      </>
                    ) : (
                      <>
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Resume Subscription
                      </>
                    )}
                  </Button>
                )}

                {subscriptionStatus?.urls?.update_payment_method && (
                  <Button
                    variant="outline"
                    onClick={handleUpdatePaymentMethod}
                    className="border-blue-200 hover:bg-blue-50 hover:border-blue-300 text-blue-600 transition-all"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                    </svg>
                    Update Payment Method
                  </Button>
                )}
              </div>
            </div>
          </div>

   {/* Billing toggle for monthly/annually */}
   <div className="flex items-center justify-center mb-12">
            <span className={`text-sm mr-3 ${billingInterval === 'monthly' ? 'font-semibold text-gray-900' : 'text-gray-500'}`}>Monthly</span>
            <button
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${billingInterval === 'annual' ? 'bg-blue-600' : 'bg-gray-200'}`}
              onClick={toggleBillingInterval}
              role="switch"
              aria-checked={billingInterval === 'annual'}
            >
              <span
                className={`${billingInterval === 'annual' ? 'translate-x-6' : 'translate-x-1'} inline-block h-4 w-4 transform rounded-full bg-white transition-transform`}
              />
            </button>
            <div className="flex items-center ml-3">
              <span className={`text-sm ${billingInterval === 'annual' ? 'font-semibold text-gray-900' : 'text-gray-500'}`}>Yearly</span>
              <div className="flex items-center ml-2">
                <span className="rounded-full bg-blue-100 px-2 py-0.5 text-xs font-medium text-blue-800 text-center">2 months free</span>
                <span className="mx-1 text-gray-500 font-bold">+</span>
                <span className="rounded-full bg-green-100 px-2 py-0.5 text-xs font-medium text-green-800 text-center">Extra 30% off</span>
              </div>
            </div>
          </div>

          {/* Pricing Tiers */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-16">
            {Object.entries(SUBSCRIPTION_PRICING)
              .filter(([key]) => key !== 'FREE') // Filter out the FREE tier
              .map(([key, tier]) => (
              <div key={key} className="flex flex-col">
                <div className="h-8 relative">
                  {key === 'PRO' && (
                    <div className="absolute top-0 left-1/2 transform -translate-x-1/2 z-20">
                      <span className="bg-blue-500 text-white text-xs font-bold py-1.5 px-2 xl:px-4 rounded-full shadow-md inline-block">
                        🔥 Most popular
                      </span>
                    </div>
                  )}
                </div>
                <div
                  className={`rounded-xl overflow-hidden bg-white border ${
                    user?.subscriptionTier === key ? 'border-blue-500' : 'border-gray-200'
                  } shadow-sm flex-1 ${key === 'PRO' ? 'md:scale-y-105 z-10' : ''}`}
                >
                  {/* Plan header */}
                  <div className={`${
                    key === 'PRO' ? 'bg-blue-200' : 'bg-blue-100'
                  } p-6`}>
                    <div className="flex items-center mb-3">
                      <div className={`${
                        key === 'PRO' ? 'bg-blue-600' : 'bg-blue-500'
                      } w-10 h-10 rounded-full flex items-center justify-center mr-3`}>
                        <span className="text-white font-bold">{tier.name.charAt(0)}</span>
                      </div>
                      <div>
                        <h3 className="text-xl font-bold">{tier.name}</h3>
                        <p className="text-sm text-gray-600">
                          {key === 'PRO' ? 'For professionals' : 'For teams'}
                        </p>
                      </div>
                    </div>
                    <p className="text-sm text-gray-700 mb-4">
                      {key === 'PRO' ? 'Advanced features with unlimited banner creation' :
                       'Enterprise-grade features for teams and agencies'}
                    </p>

                    {/* Pricing */}
                    <div className="mb-4">
                      {billingInterval === 'monthly' ? (
                        <div className="flex items-center">
                          <span className="text-4xl font-bold">${tier.monthly.price}</span>
                          {tier.monthly.price !== 0 && <span className="text-gray-600 ml-1">/user/month</span>}
                        </div>
                      ) : (
                        <div>
                          <div className="flex items-center">
                            <span className="text-4xl font-bold">${Math.round(tier.annual.price / 10)}</span>
                            {tier.monthly.price !== 0 && (
                              <span className="text-gray-600 ml-1">/user/month
                                <span className="block text-sm">billed annually as ${tier.annual.price}</span>
                              </span>
                            )}
                          </div>
                          {tier.monthly.price !== 0 && (
                            <div className="mt-1 flex flex-col">
                              <div className="flex items-center">
                                <span className="text-md text-gray-500 line-through">${tier.monthly.price}</span>
                              </div>
                              <span className="text-sm text-blue-600 font-medium">Extra 30% off for early adopters</span>
                            </div>
                          )}
                        </div>
                      )}
                    </div>

                    {/* CTA Button */}
                    <Button
                      className={`w-full ${
                        key === 'PRO'
                          ? 'bg-blue-900 hover:bg-blue-800 text-white'
                          : 'bg-white hover:bg-gray-100 text-gray-800 border border-gray-300'
                      } ${user?.subscriptionTier === key ? 'opacity-75 cursor-not-allowed' : ''}`}
                      variant={key === 'PRO' ? 'default' : 'outline'}
                      onClick={() => handleUpgrade(key)}
                      disabled={user?.subscriptionTier === key || processingAction}
                    >
                      {processingAction ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Processing...
                        </>
                      ) : user?.subscriptionTier === key ? (
                        'Current Plan'
                      ) : user?.subscriptionTier === 'FREE' || !user?.subscriptionTier ? (
                        'Upgrade'
                      ) : (
                        'Switch Plan'
                      )}
                    </Button>
                  </div>

                  {/* Features */}
                  <div className="p-6">
                    <div className="mb-4">
                      {(
                        <div className="flex items-center">
                          <div className="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center mr-2">
                            <span className="text-blue-500 text-xs">∞</span>
                          </div>
                          <p className="text-sm text-gray-600">Unlimited banners</p>
                        </div>
                      )}
                    </div>

                    <h4 className="font-medium mb-3 text-sm">Key features included in {tier.name}:</h4>
                    <ul className="space-y-3 mb-6">
                      {tier.features.map((feature) => (
                        <li key={feature} className="flex items-start">
                          <HiCheck className="h-5 w-5 flex-shrink-0 text-blue-500 mt-0.5" />
                          <span className="ml-2 text-sm text-gray-600">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Cancel Subscription Confirmation Modal */}
      <Dialog open={showCancelModal} onOpenChange={setShowCancelModal}>
        <DialogContent className="bg-white rounded-xl border border-blue-100 shadow-xl">
          <DialogHeader className="mb-4">
            <DialogTitle className="text-2xl font-bold text-gray-800">Cancel Subscription</DialogTitle>
            <DialogDescription className="text-gray-600 mt-2">
              Are you sure you want to cancel your subscription? You'll still have access to your current plan until the end of your billing period.
            </DialogDescription>
          </DialogHeader>

          <div className="bg-blue-50 border border-blue-100 rounded-lg p-4 my-4">
            <div className="flex items-start gap-3">
              <div className="text-blue-500 mt-0.5">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <p className="text-sm text-blue-700">
                If you cancel, your subscription will remain active until the end of your current billing period. After that, you'll be downgraded to the Free plan.
              </p>
            </div>
          </div>

          <DialogFooter className="flex flex-col sm:flex-row gap-3 mt-2">
            <Button
              variant="outline"
              onClick={() => setShowCancelModal(false)}
              disabled={processingAction}
              className="w-full sm:w-auto border-blue-200 text-blue-600 hover:bg-blue-50 hover:border-blue-300"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              Keep My Subscription
            </Button>
            <Button
              variant="destructive"
              onClick={handleCancelSubscription}
              disabled={processingAction}
              className="w-full sm:w-auto bg-red-600 hover:bg-red-700"
            >
              {processingAction ? (
                <>
                  <HiOutlineRefresh className="mr-2 h-4 w-4 animate-spin" />
                  Cancelling...
                </>
              ) : (
                <>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                  Cancel Subscription
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
