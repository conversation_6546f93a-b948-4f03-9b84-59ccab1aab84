'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { HiCheckCircle } from 'react-icons/hi';
import Navbar from '@/components/dashboard/Navbar';

export default function SubscriptionSuccess() {
  const router = useRouter();
  const { loading, refreshUserProfile } = useAuth();
  const [countdown, setCountdown] = useState<number>(5);

  useEffect(() => {
    // Get URL parameters
    const params = new URLSearchParams(window.location.search);
    const orderId = params.get('order_id');
    const redirectUrl = params.get('redirect_url');

    console.log('Success page loaded with params:', { orderId, redirectUrl });

    // Refresh the user data to get the updated subscription status
    refreshUserProfile();

    // If we have a redirect URL from Lemon Squeezy, use it
    if (redirectUrl) {
      try {
        const url = new URL(redirectUrl);
        // Only redirect to URLs on our domain for security
        if (url.hostname === window.location.hostname) {
          router.push(redirectUrl);
          return;
        }
      } catch (error) {
        console.error('Invalid redirect URL:', redirectUrl);
      }
    }

    // Redirect to the subscription page after 5 seconds
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          router.push('/subscription');
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [router, refreshUserProfile]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-gray-50">
        <Navbar />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 flex flex-col items-center justify-center">
          <div className="w-full max-w-md p-8 bg-white rounded-xl shadow-md text-center border border-blue-100">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <h1 className="text-2xl font-bold mt-6 mb-2">Loading...</h1>
            <p className="text-gray-600">Please wait while we verify your subscription.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-gray-50">
      <Navbar />
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 flex flex-col items-center justify-center">
        <div className="w-full max-w-md p-8 bg-white rounded-xl shadow-md text-center border border-blue-100">
          <div className="mx-auto w-16 h-16 flex items-center justify-center rounded-full bg-green-100">
            <HiCheckCircle className="h-10 w-10 text-green-600" />
          </div>

          <h1 className="text-2xl font-bold mt-6 mb-2">Thank You!</h1>
          <p className="text-gray-600 mb-6">
            Your subscription has been successfully processed. You now have access to all the features of your new plan.
          </p>

          <div className="text-sm text-gray-500 mb-6">
            Redirecting to subscription page in {countdown} seconds...
          </div>

          <Button
            onClick={() => router.push('/subscription')}
            className="w-full bg-blue-600 hover:bg-blue-700"
          >
            Go to Subscription Page
          </Button>
        </div>
      </div>
    </div>
  );
}
