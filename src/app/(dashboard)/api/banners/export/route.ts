import { NextResponse } from 'next/server';
import { withAuth } from '@/middleware/withAuth';

// Use force-dynamic to ensure we always get fresh data
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;

export const POST = withAuth(async (req, supabase, user) => {
  try {
    const { bannerId, projectId } = await req.json();

    if (!bannerId || !projectId) {
      return NextResponse.json(
        { error: 'Banner ID and Project ID are required' },
        { status: 400 }
      );
    }

    // Verify banner access
    const { data: banner, error: bannerError } = await supabase
      .from('banners')
      .select('userId')
      .eq('id', bannerId)
      .single();

    if (bannerError || !banner) {
      return NextResponse.json({ error: 'Banner not found' }, { status: 404 });
    }

    if (banner.userId !== user.id) {
      return NextResponse.json({ error: 'Unauthorized access to banner' }, { status: 403 });
    }

    // Generate export URL
    const exportUrl = `${process.env.NEXT_PUBLIC_APP_URL}/api/banners/${bannerId}/export`;

    // Update banner with export URL
    const { data: updatedBanner, error: updateError } = await supabase
      .from('banners')
      .update({
        exportUrl,
        exportedAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        updatedBy: user.id
      })
      .eq('id', bannerId)
      .select()
      .single();

    if (updateError) {
      console.error('Error updating banner with export URL:', updateError);
      return NextResponse.json({ error: 'Failed to update banner' }, { status: 500 });
    }

    return NextResponse.json({ banner: updatedBanner });
  } catch (error) {
    console.error('Error in banner export API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
});