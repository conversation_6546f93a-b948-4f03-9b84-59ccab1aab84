import { NextResponse } from 'next/server';
import { createSSRClient } from '@/lib/supabase/server';

export const dynamic = 'force-dynamic';

export async function GET(request) {
  try {
    const supabase = await createSSRClient();

    // Get the banner ID from the URL
    const { searchParams } = new URL(request.url);
    const bannerId = searchParams.get('id');

    if (!bannerId) {
      return NextResponse.json({ error: 'Banner ID is required' }, { status: 400 });
    }

    // Get the banner data
    const { data: banner, error } = await supabase
      .from('banners')
      .select('*')
      .eq('id', bannerId)
      .single();

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    if (!banner) {
      return NextResponse.json({ error: 'Banner not found' }, { status: 404 });
    }

    return NextResponse.json({ banner });
  } catch (error) {
    console.error('Preview error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request) {
  try {
    const supabase = await createSSRClient();

    // Get the banner data from the request body
    const body = await request.json();
    const { bannerId, previewData } = body;

    if (!bannerId || !previewData) {
      return NextResponse.json({ error: 'Banner ID and preview data are required' }, { status: 400 });
    }

    // Update the banner preview data
    const { data: banner, error } = await supabase
      .from('banners')
      .update({ preview_data: previewData })
      .eq('id', bannerId)
      .select()
      .single();

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ banner });
  } catch (error) {
    console.error('Preview update error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}