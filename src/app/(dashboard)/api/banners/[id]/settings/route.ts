import { NextResponse } from 'next/server';
import { withAuth } from '@/middleware/withAuth';

export const GET = withAuth(async (req, supabase, user) => {
  try {
    console.log('Banner settings API - req.params:', req.params);
    const { id } = req.params;

    if (!id || id === 'undefined') {
      console.error('Invalid banner ID in settings API:', id);
      return NextResponse.json({ error: 'Invalid banner ID' }, { status: 400 });
    }

    const { data: banner, error } = await supabase
      .from('banners')
      .select('*')
      .eq('id', id)
      .eq('userId', user.id)
      .single();

    if (error) {
      console.error('Error fetching banner settings:', error);
      return NextResponse.json({ error: 'Banner not found' }, { status: 404 });
    }

    return NextResponse.json({ settings: banner });
  } catch (error) {
    console.error('Error in GET banner settings API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
});

export const PUT = withAuth(async (req, supabase, user) => {
  try {
    console.log('Banner settings PUT API - req.params:', req.params);
    const { id } = req.params;

    if (!id || id === 'undefined') {
      console.error('Invalid banner ID in settings PUT API:', id);
      return NextResponse.json({ error: 'Invalid banner ID' }, { status: 400 });
    }

    const settings = await req.json();

    // First verify the banner belongs to the user
    const { data: existingBanner, error: existingError } = await supabase
      .from('banners')
      .select('userId')
      .eq('id', id)
      .single();

    if (existingError) {
      console.error('Error fetching banner:', existingError);
      return NextResponse.json({ error: 'Banner not found' }, { status: 404 });
    }

    if (existingBanner.userId !== user.id) {
      return NextResponse.json({ error: 'Unauthorized access to banner' }, { status: 403 });
    }

    // Update banner settings
    const { data: updatedBanner, error: updateError } = await supabase
      .from('banners')
      .update(settings)
      .eq('id', id)
      .select()
      .single();

    if (updateError) {
      console.error('Error updating banner settings:', updateError);
      return NextResponse.json({ error: 'Failed to update banner settings' }, { status: 500 });
    }

    return NextResponse.json({ banner: updatedBanner });
  } catch (error) {
    console.error('Error in PUT banner settings API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
});