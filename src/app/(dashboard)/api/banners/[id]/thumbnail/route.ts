import { NextResponse } from 'next/server';
import { withAuth } from '@/middleware/withAuth';
import fs from 'fs';
import path from 'path';
import { createId } from '@paralleldrive/cuid2';

/**
 * Processes a thumbnail asynchronously
 * This function is not awaited by the main request handler
 */
async function processThumbnailAsync(buffer, id, supabase) {
  console.log(`Starting async thumbnail processing for banner ${id}`);
  try {
    // Generate a unique filename
    const filename = `banner_${id}_${Date.now()}.png`;

    // Try to upload to Supabase Storage first
    let thumbnailUrl;
    try {
      // Upload the thumbnail to Supabase Storage
      const { data: uploadData, error: uploadError } = await supabase
        .storage
        .from('thumbnails')
        .upload(filename, buffer, {
          contentType: 'image/png',
          upsert: true
        });

      if (uploadError) {
        // If bucket not found or other storage error, throw to trigger fallback
        if (uploadError.error === 'Bucket not found' || uploadError.statusCode === '404' || uploadError.message === 'Bucket not found') {
          console.log('Bucket not found, using local storage fallback');
          throw new Error('Bucket not found, using local storage fallback');
        }
        console.error('Error uploading thumbnail to storage:', uploadError);
        return;
      }

      // Get the public URL
      const { data: urlData } = await supabase
        .storage
        .from('thumbnails')
        .getPublicUrl(filename);

      thumbnailUrl = urlData.publicUrl;
    } catch (storageError) {
      console.log('Using local storage fallback:', storageError.message);

      // Fallback to local file storage
      try {
        // Ensure uploads directory exists
        const uploadsDir = path.join(process.cwd(), 'public', 'uploads');
        if (!fs.existsSync(uploadsDir)) {
          fs.mkdirSync(uploadsDir, { recursive: true });
        }

        // Generate a unique filename
        const localFilename = `banner_${id}_${createId()}.png`;
        const filePath = path.join(uploadsDir, localFilename);

        // Write the file
        await fs.promises.writeFile(filePath, buffer);

        // Set the URL to the local path
        thumbnailUrl = `/uploads/${localFilename}`;
      } catch (localStorageError) {
        console.error('Error saving to local storage:', localStorageError);
        return;
      }
    }

    // Update the banner with the thumbnail URL
    const { data, error } = await supabase
      .from('banners')
      .update({ thumbnail: thumbnailUrl })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating banner with thumbnail URL:', error);
      return;
    }

    console.log(`Async thumbnail processing completed for banner ${id}`);
  } catch (error) {
    console.error(`Error in async thumbnail processing for banner ${id}:`, error);
  }
}

export const POST = withAuth(async (req, supabase, user) => {
  try {
    const { params } = req;
    const id = params.id;

    // Get the form data
    const formData = await req.formData();
    const thumbnail = formData.get('thumbnail');

    if (!thumbnail) {
      return NextResponse.json({ error: 'No thumbnail provided' }, { status: 400 });
    }

    // Convert the file to a buffer
    const buffer = Buffer.from(await thumbnail.arrayBuffer());

    // Check if this is a non-blocking request (client doesn't need to wait for processing)
    const nonBlocking = formData.get('nonBlocking') === 'true';

    if (nonBlocking) {
      // Start processing in the background without awaiting
      // This allows the request to return immediately
      processThumbnailAsync(buffer, id, supabase).catch(error => {
        console.error('Unhandled error in background thumbnail processing:', error);
      });

      // Return success immediately
      return NextResponse.json({
        success: true,
        message: 'Thumbnail processing started in background',
        thumbnailPending: true
      });
    } else {
      // For backward compatibility, process synchronously if nonBlocking is not specified
      // Generate a unique filename
      const filename = `banner_${id}_${Date.now()}.png`;

      // Try to upload to Supabase Storage first
      let thumbnailUrl;
      try {
        // Upload the thumbnail to Supabase Storage
        const { data: uploadData, error: uploadError } = await supabase
          .storage
          .from('thumbnails')
          .upload(filename, buffer, {
            contentType: 'image/png',
            upsert: true
          });

        if (uploadError) {
          // If bucket not found or other storage error, throw to trigger fallback
          if (uploadError.error === 'Bucket not found' || uploadError.statusCode === '404' || uploadError.message === 'Bucket not found') {
            console.log('Bucket not found, using local storage fallback');
            throw new Error('Bucket not found, using local storage fallback');
          }
          console.error('Error uploading thumbnail to storage:', uploadError);
          return NextResponse.json({ error: 'Failed to upload thumbnail' }, { status: 500 });
        }

        // Get the public URL
        const { data: urlData } = await supabase
          .storage
          .from('thumbnails')
          .getPublicUrl(filename);

        thumbnailUrl = urlData.publicUrl;
      } catch (storageError) {
        console.log('Using local storage fallback:', storageError.message);

        // Fallback to local file storage
        try {
          // Ensure uploads directory exists
          const uploadsDir = path.join(process.cwd(), 'public', 'uploads');
          if (!fs.existsSync(uploadsDir)) {
            fs.mkdirSync(uploadsDir, { recursive: true });
          }

          // Generate a unique filename
          const localFilename = `banner_${id}_${createId()}.png`;
          const filePath = path.join(uploadsDir, localFilename);

          // Write the file
          await fs.promises.writeFile(filePath, buffer);

          // Set the URL to the local path
          thumbnailUrl = `/uploads/${localFilename}`;
        } catch (localStorageError) {
          console.error('Error saving to local storage:', localStorageError);
          return NextResponse.json({ error: 'Failed to save thumbnail locally' }, { status: 500 });
        }
      }

      // Update the banner with the thumbnail URL
      const { data, error } = await supabase
        .from('banners')
        .update({ thumbnail: thumbnailUrl })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Error updating banner with thumbnail URL:', error);
        return NextResponse.json({ error: 'Failed to update banner' }, { status: 500 });
      }

      return NextResponse.json({
        success: true,
        thumbnail: thumbnailUrl,
        banner: data
      });
    }
  } catch (error) {
    console.error('Error in POST /api/banners/[id]/thumbnail:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
});
