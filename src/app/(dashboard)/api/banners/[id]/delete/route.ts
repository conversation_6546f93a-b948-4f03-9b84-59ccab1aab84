import { NextResponse } from 'next/server';
import { withAuth } from '@/middleware/withAuth';
import { createErrorResponse, isValidUUID } from '@/constants/bannerFields';

export const dynamic = 'force-dynamic';

/**
 * DELETE /api/banners/[id]/delete
 * Delete a banner by ID
 */
export const DELETE = withAuth(async (req: any, supabase: any, user: any) => {
  try {
    const { id } = req.params;

    // Validate ID is a valid UUID
    if (!id || !isValidUUID(id)) {
      return createErrorResponse('Invalid banner ID', 400);
    }

    // Verify banner access
    const { data: banner, error: bannerError } = await supabase
      .from('banners')
      .select('userId')
      .eq('id', id)
      .single();

    if (bannerError) {
      // If this is a "no rows returned" error, return 404
      if (bannerError.code === 'PGRST116') {
        return createErrorResponse('Banner not found', 404);
      }
      return createErrorResponse('Failed to fetch banner', 500, bannerError);
    }

    if (banner.userId !== user.id) {
      return createErrorResponse('Unauthorized access to banner', 403);
    }

    // Delete the banner
    const { error: deleteError } = await supabase
      .from('banners')
      .delete()
      .eq('id', id);

    if (deleteError) {
      return createErrorResponse('Failed to delete banner', 500, deleteError);
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    return createErrorResponse('Internal server error', 500, error);
  }
});
