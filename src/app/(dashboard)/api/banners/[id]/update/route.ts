import { NextResponse } from 'next/server';
import { withAuth } from '@/middleware/withAuth';
import { 
  BANNER_DEFAULT_VALUES, 
  createErrorResponse, 
  isValidUUID,
  processBannerElements
} from '@/constants/bannerFields';

export const dynamic = 'force-dynamic';

/**
 * PUT /api/banners/[id]/update
 * Update a banner by ID
 */
export const PUT = withAuth(async (req: any, supabase: any, user: any) => {
  try {
    const { id } = req.params;

    // Validate ID is a valid UUID
    if (!id || !isValidUUID(id)) {
      return createErrorResponse('Invalid banner ID', 400);
    }

    // Parse request body
    const body = await req.json();
    const { ...updateData } = body;

    // Verify banner access
    const { data: banner, error: bannerError } = await supabase
      .from('banners')
      .select('userId')
      .eq('id', id)
      .single();

    if (bannerError || !banner) {
      return createErrorResponse('Banner not found', 404, bannerError);
    }

    if (banner.userId !== user.id) {
      return createErrorResponse('Unauthorized access to banner', 403);
    }

    // Process elements if present
    if (updateData.elements) {
      updateData.elements = Array.isArray(updateData.elements) 
        ? JSON.stringify(updateData.elements) 
        : updateData.elements;
    }

    // Update banner
    const { data: updatedBanner, error } = await supabase
      .from('banners')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      return createErrorResponse('Failed to update banner', 500, error);
    }

    // Process banner elements for response
    const processedBanner = {
      ...updatedBanner,
      elements: processBannerElements(updatedBanner.elements)
    };

    return NextResponse.json({ banner: processedBanner });
  } catch (error) {
    return createErrorResponse('Internal server error', 500, error);
  }
});
