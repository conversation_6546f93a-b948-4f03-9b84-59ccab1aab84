import { NextResponse } from 'next/server';
import { withAuth } from '@/middleware/withAuth';

export const dynamic = 'force-dynamic';

/**
 * GET /api/banners/count
 * Get the count of banners for a project
 * Required query param: projectId
 */
export const GET = withAuth(async (req, supabase, user) => {
  try {
    const { searchParams } = new URL(req.url);
    const projectId = searchParams.get('projectId');

    if (!projectId) {
      return NextResponse.json({ error: 'Project ID is required' }, { status: 400 });
    }

    // Verify project belongs to user
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('id')
      .eq('id', projectId)
      .eq('userId', user.id)
      .single();

    if (projectError || !project) {
      console.error('Error verifying project access:', projectError);
      return NextResponse.json({ error: 'Project not found or access denied' }, { status: 403 });
    }

    // Count banners in the project
    const { count, error } = await supabase
      .from('banners')
      .select('id', { count: 'exact', head: true })
      .eq('projectId', projectId)
      .eq('userId', user.id);

    if (error) {
      console.error('Error counting banners:', error);
      return NextResponse.json({ error: 'Failed to count banners' }, { status: 500 });
    }

    return NextResponse.json({ count });
  } catch (error) {
    console.error('Error in GET /api/banners/count:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
});
