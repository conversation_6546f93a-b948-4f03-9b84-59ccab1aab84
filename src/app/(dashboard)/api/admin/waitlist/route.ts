import { NextResponse } from 'next/server';
import { createSSRClient } from '@/lib/supabase/server';

export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;

// GET /api/admin/waitlist - Get all waitlist entries
export async function GET(req) {
  try {
    // Create Supabase client using our utility function
    const supabase = await createSSRClient();

    // Get the authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      console.error('User not authenticated:', userError);
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // In this application, all users are set as admin in the AuthContext
    // For a production app, you would check user roles in the database
    console.log('Admin user authenticated:', user.id, user.email);

    // Parse query parameters
    const url = new URL(req.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const search = url.searchParams.get('search') || '';

    // Calculate offset
    const offset = (page - 1) * limit;

    // Build query
    let query = supabase
      .from('waitlist')
      .select('*', { count: 'exact' });

    // Add search filter if provided
    if (search) {
      query = query.or(`name.ilike.%${search}%,email.ilike.%${search}%`);
    }

    // Add pagination
    query = query.order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    // Execute query
    console.log('Executing waitlist query with range:', offset, 'to', offset + limit - 1);
    const { data, error, count } = await query;

    if (error) {
      console.error('Error fetching waitlist entries:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    console.log('Waitlist query results:', { count, entries: data?.length });

    // Calculate pagination info
    const totalPages = Math.ceil(count / limit);

    return NextResponse.json({
      entries: data,
      pagination: {
        total: count,
        page,
        limit,
        totalPages
      }
    });
  } catch (error) {
    console.error('Error in GET /api/admin/waitlist:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}


