import { NextResponse } from 'next/server';
import { createSSRClient } from '@/lib/supabase/server';

export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;

// DELETE /api/admin/waitlist/:id - Delete a waitlist entry
export async function DELETE(req, { params }) {
  try {
    // Get the ID from the route params
    const { id } = params;

    console.log('Deleting waitlist entry with ID:', id);

    if (!id) {
      return NextResponse.json({ error: 'Entry ID is required' }, { status: 400 });
    }

    // Create Supabase client using our utility function
    const supabase = await createSSRClient();

    // Get the authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      console.error('User not authenticated:', userError);
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // In this application, all users are set as admin in the AuthContext
    console.log('Admin user authenticated for delete:', user.id, user.email);

    // Delete the entry
    const { error } = await supabase
      .from('waitlist')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting waitlist entry:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error in DELETE /api/admin/waitlist/:id:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
