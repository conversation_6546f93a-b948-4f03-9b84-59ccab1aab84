import { NextResponse } from 'next/server';
import { withAuth } from '@/middleware/withAuth';
import slugify from 'slugify';

export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;

// Helper function to generate a slug from a name
function generateSlug(name) {
  return slugify(name, { lower: true });
}

export const PUT = withAuth(async (req, supabase, user) => {
  try {
    const { id } = req.params;
    const body = await req.json();
    
    // Validate required fields
    if (!body.name) {
      return NextResponse.json(
        { error: 'Name is required' },
        { status: 400 }
      );
    }

    // Generate a slug from the name
    const slug = generateSlug(body.name);
    
    // Check if slug already exists for a different category
    const { data: existingCategory, error: slugCheckError } = await supabase
      .from('template_categories')
      .select('id')
      .eq('slug', slug)
      .neq('id', id)
      .maybeSingle();
      
    if (slugCheckError) {
      console.error('Error checking slug uniqueness:', slugCheckError);
      return NextResponse.json({ error: 'Error checking slug uniqueness' }, { status: 500 });
    }
    
    // If slug exists for a different category, make it unique
    const uniqueSlug = existingCategory ? `${slug}-${Date.now()}` : slug;
    
    const { data: category, error } = await supabase
      .from('template_categories')
      .update({
        name: body.name,
        description: body.description || '',
        slug: uniqueSlug
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating category:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json(category);
  } catch (error) {
    console.error('Error in PUT /api/template-categories/[id]:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
});

export const DELETE = withAuth(async (req, supabase, user) => {
  try {
    const { id } = req.params;
    console.log('Deleting category with ID:', id);

    // Check if category has subcategories
    const { data: subcategories, error: subcategoriesError } = await supabase
      .from('template_subcategories')
      .select('id')
      .eq('categoryId', id);
      
    if (subcategoriesError) {
      console.error('Error checking subcategories:', subcategoriesError);
      return NextResponse.json({ error: 'Error checking subcategories' }, { status: 500 });
    }
    
    if (subcategories && subcategories.length > 0) {
      return NextResponse.json(
        { error: 'Cannot delete category with subcategories. Please delete subcategories first.' },
        { status: 400 }
      );
    }

    const { error } = await supabase
      .from('template_categories')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting category:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error in DELETE /api/template-categories/[id]:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
});
