import { NextResponse } from 'next/server';
import { withAuth } from '@/middleware/withAuth';
import slugify from 'slugify';

export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;

// Helper function to generate a slug from a name
function generateSlug(name) {
  return slugify(name, { lower: true });
}

export const GET = withAuth(async (req, supabase, user) => {
  try {
    // Get categories with template count - use a more efficient query
    const { data: categories, error } = await supabase
      .from('template_categories')
      .select(`
        id,
        name,
        description,
        slug,
        templates_count:templates(count)
      `)
      .order('name');

    if (error) throw error;

    // Process the data to ensure templates_count is a number, not an object
    const processedCategories = categories.map(category => ({
      ...category,
      templates_count: category.templates_count ? parseInt(category.templates_count.count, 10) || 0 : 0
    }));

    // Return a simpler response format
    return NextResponse.json(processedCategories);
  } catch (error) {
    console.error('Error in GET /api/template-categories:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
});

export const POST = withAuth(async (req, supabase, user) => {
  try {
    const body = await req.json();
    console.log('Creating category with body:', body);

    // Validate required fields
    if (!body.name) {
      return NextResponse.json(
        { error: 'Name is required' },
        { status: 400 }
      );
    }

    // Check if name already exists
    const { data: existingCategoryByName, error: nameCheckError } = await supabase
      .from('template_categories')
      .select('id')
      .eq('name', body.name)
      .maybeSingle();

    if (nameCheckError) {
      console.error('Error checking name uniqueness:', nameCheckError);
      return NextResponse.json({ error: 'Error checking name uniqueness' }, { status: 500 });
    }

    if (existingCategoryByName) {
      console.error('Category with this name already exists');
      return NextResponse.json({ error: 'A category with this name already exists' }, { status: 400 });
    }

    // Generate a slug from the name
    const slug = generateSlug(body.name);

    // Check if slug already exists
    const { data: existingCategory, error: slugCheckError } = await supabase
      .from('template_categories')
      .select('id')
      .eq('slug', slug)
      .maybeSingle();

    if (slugCheckError) {
      console.error('Error checking slug uniqueness:', slugCheckError);
      return NextResponse.json({ error: 'Error checking slug uniqueness' }, { status: 500 });
    }

    // If slug exists, make it unique by adding a timestamp
    const uniqueSlug = existingCategory ? `${slug}-${Date.now()}` : slug;
    console.log('Using slug:', uniqueSlug);

    const { data: categories, error } = await supabase
      .from('template_categories')
      .insert([{
        name: body.name,
        description: body.description || '',
        slug: uniqueSlug
      }])
      .select()
      .single();

    if (error) {
      console.error('Error inserting category:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    console.log('Category created successfully:', categories);
    return NextResponse.json(categories);
  } catch (error) {
    console.error('Error in POST /api/template-categories:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
});

export const PUT = withAuth(async (req, supabase, user) => {
  try {
    const body = await req.json();
    const { id, ...updateData } = body;

    // If name is being updated, update the slug as well
    let updatedData = { ...updateData };
    if (updateData.name) {
      const slug = generateSlug(updateData.name);

      // Check if slug already exists for a different category
      const { data: existingCategory, error: slugCheckError } = await supabase
        .from('template_categories')
        .select('id')
        .eq('slug', slug)
        .neq('id', id)
        .maybeSingle();

      if (slugCheckError) {
        console.error('Error checking slug uniqueness:', slugCheckError);
        return NextResponse.json({ error: 'Error checking slug uniqueness' }, { status: 500 });
      }

      // If slug exists for a different category, make it unique
      updatedData.slug = existingCategory ? `${slug}-${Date.now()}` : slug;
    }

    const { data: category, error } = await supabase
      .from('template_categories')
      .update(updatedData)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;

    return NextResponse.json(category);
  } catch (error) {
    console.error('Error in PUT /api/template-categories:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
});

export const DELETE = withAuth(async (req, supabase, user) => {
  try {
    const { searchParams } = new URL(req.url);
    const id = searchParams.get('id');

    const { error } = await supabase
      .from('template_categories')
      .delete()
      .eq('id', id);

    if (error) throw error;

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error in DELETE /api/template-categories:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
});