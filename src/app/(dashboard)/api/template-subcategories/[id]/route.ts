import { NextResponse } from 'next/server';
import { withAuth } from '@/middleware/withAuth';
import slugify from 'slugify';

export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;

// Helper function to generate a slug from a name
function generateSlug(name) {
  return slugify(name, { lower: true });
}

export const PUT = withAuth(async (req, supabase, user) => {
  try {
    const { id } = req.params;
    const body = await req.json();

    // Validate required fields
    if (!body.name || !body.categoryId) {
      return NextResponse.json(
        { error: 'Name and category are required' },
        { status: 400 }
      );
    }

    // Generate a new slug if the name has changed
    // First, get the current subcategory to check if name has changed
    const { data: currentSubcategory, error: fetchError } = await supabase
      .from('template_subcategories')
      .select('name')
      .eq('id', id)
      .single();

    if (fetchError) throw fetchError;

    // Prepare update data
    const updateData = {
      name: body.name,
      description: body.description || '',
      categoryId: body.categoryId
    };

    // If name has changed, generate a new slug
    if (currentSubcategory && currentSubcategory.name !== body.name) {
      const slug = generateSlug(body.name);

      // Check if slug already exists for this category
      const { data: existingSubcategory, error: slugCheckError } = await supabase
        .from('template_subcategories')
        .select('id')
        .eq('slug', slug)
        .eq('categoryId', body.categoryId)
        .neq('id', id)
        .maybeSingle();

      if (slugCheckError) {
        console.error('Error checking slug uniqueness:', slugCheckError);
        return NextResponse.json({ error: 'Error checking slug uniqueness' }, { status: 500 });
      }

      // If slug exists for this category, make it unique
      if (updateData as any) {
        (updateData as any).slug = existingSubcategory ? `${slug}-${Date.now()}` : slug;
      }
    }

    const { data, error } = await supabase
      .from('template_subcategories')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in PUT /api/template-subcategories/[id]:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
});

export const DELETE = withAuth(async (req, supabase, user) => {
  try {
    const { id } = req.params;

    const { error } = await supabase
      .from('template_subcategories')
      .delete()
      .eq('id', id);

    if (error) throw error;

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error in DELETE /api/template-subcategories/[id]:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
});
