import { NextResponse } from 'next/server';
import { withAuth } from '@/middleware/withAuth';
import slugify from 'slugify';

export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;

// Helper function to generate a slug from a name
function generateSlug(name) {
  return slugify(name, { lower: true });
}

export const GET = withAuth(async (req, supabase, user) => {
  try {
    // Check if categoryId is provided as a query parameter
    const { searchParams } = new URL(req.url);
    const categoryId = searchParams.get('categoryId');

    // Build an optimized query that includes template counts in a single round-trip
    // This avoids the N+1 query problem by using Supabase's aggregation capabilities
    let query = supabase
      .from('template_subcategories')
      .select(`
        id,
        name,
        description,
        slug,
        categoryId,
        category:template_categories(id, name),
        templates:templates!subcategoryId(count)
      `);

    // Filter by categoryId if provided
    if (categoryId) {
      query = query.eq('categoryId', categoryId);
    }

    // Execute the query with ordering
    const { data, error } = await query.order('name');

    if (error) {
      console.error('Supabase query error:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Transform the response to maintain the same structure
    // Map the templates count to templates_count for backward compatibility
    const formattedData = (data || []).map(subcategory => ({
      ...subcategory,
      templates_count: subcategory.templates?.count || 0
    }));

    return NextResponse.json(formattedData);
  } catch (error) {
    console.error('Error in GET /api/template-subcategories:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
});

export const POST = withAuth(async (req, supabase, user) => {
  try {
    const body = await req.json();

    // Validate required fields
    if (!body.name || !body.categoryId) {
      return NextResponse.json(
        { error: 'Name and category are required' },
        { status: 400 }
      );
    }

    // Generate a slug from the name
    const slug = generateSlug(body.name);

    // Check if slug already exists for this category
    const { data: existingSubcategory, error: slugCheckError } = await supabase
      .from('template_subcategories')
      .select('id')
      .eq('slug', slug)
      .eq('categoryId', body.categoryId)
      .maybeSingle();

    if (slugCheckError) {
      console.error('Error checking slug uniqueness:', slugCheckError);
      return NextResponse.json({ error: 'Error checking slug uniqueness' }, { status: 500 });
    }

    // If slug exists for this category, make it unique
    const uniqueSlug = existingSubcategory ? `${slug}-${Date.now()}` : slug;

    const { data, error } = await supabase
      .from('template_subcategories')
      .insert({
        name: body.name,
        description: body.description || '',
        categoryId: body.categoryId,
        slug: uniqueSlug
      })
      .select()
      .single();

    if (error) throw error;

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in POST /api/template-subcategories:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
});
