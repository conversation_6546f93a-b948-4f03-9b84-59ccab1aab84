import { NextResponse } from 'next/server';
import { createSSRClient } from '@/lib/supabase/server';

/**
 * Debug endpoint to check the user's subscription status
 */
export async function GET(request: Request) {
  try {
    // Create Supabase client
    const supabase = await createSSRClient();
    
    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get the user's profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single();
      
    if (profileError) {
      return NextResponse.json(
        { error: 'Failed to fetch profile', details: profileError },
        { status: 500 }
      );
    }
    
    // Get the user's subscription
    const { data: subscription, error: subscriptionError } = await supabase
      .from('profiles')
      .select(`
        id,
        subscriptionTier,
        lemonSqueezyCustomerId,
        lemonSqueezySubscriptionId,
        lemonSqueezyVariantId,
        lemonSqueezyOrderId,
        lemonSqueezyProductId,
        lemonSqueezyRenewsAt,
        lemonSqueezyEndsAt,
        lemonSqueezyStatus,
        nextBillingDate
      `)
      .eq('id', user.id)
      .single();
      
    if (subscriptionError) {
      return NextResponse.json(
        { error: 'Failed to fetch subscription', details: subscriptionError },
        { status: 500 }
      );
    }
    
    // Return the user's subscription status
    return NextResponse.json({
      user: {
        id: user.id,
        email: user.email,
      },
      profile,
      subscription,
    });
  } catch (error) {
    console.error('Error in debug subscription endpoint:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
