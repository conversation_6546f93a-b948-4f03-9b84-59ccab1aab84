import { NextResponse } from 'next/server';
import { withAuth } from '@/middleware/withAuth';

// DELETE /api/formats/[key]
export const DELETE = withAuth(async (req, supabase, user) => {
  try {
    // Parse URL to extract the key parameter
    const url = new URL(req.url);
    const pathParts = url.pathname.split('/');
    const key = decodeURIComponent(pathParts[pathParts.length - 1]); // Get the last part of the URL path and decode it

    console.log(`DELETE /api/formats/${key}`);

    if (!key) {
      console.error('No format key provided');
      return NextResponse.json({ error: 'Format key is required' }, { status: 400 });
    }

    // Check if format exists and belongs to the user
    // Use proper parameterized query to avoid SQL injection
    const { data: format, error: fetchError } = await supabase
      .from('custom_formats')
      .select('*')
      .eq('userId', user.id)
      .or(`key.eq."${key}",name.eq."${key}"`)
      .single();

    if (fetchError) {
      console.error('Error fetching format:', fetchError);
      return NextResponse.json({
        error: `Failed to fetch format: ${fetchError.message}`
      }, { status: 500 });
    }

    if (!format) {
      console.error(`Format not found or doesn't belong to user: ${key}`);
      return NextResponse.json({
        error: `Format "${key}" not found or doesn't belong to you`
      }, { status: 404 });
    }

    console.log(`Found format to delete:`, format);

    // Delete the format using the format's ID from the database
    const { error: deleteError } = await supabase
      .from('custom_formats')
      .delete()
      .eq('id', format.id)
      .eq('userId', user.id);

    if (deleteError) {
      console.error('Error deleting format:', deleteError);
      return NextResponse.json({
        error: `Failed to delete format: ${deleteError.message}`
      }, { status: 500 });
    }

    // Check if any banners are using this format
    const { data: banners, error: bannersError } = await supabase
      .from('banners')
      .select('id, name')
      .eq('userId', user.id)
      .eq('customFormatId', format.id);

    if (bannersError) {
      console.warn('Error checking banners using format:', bannersError);
      // Continue anyway since the format was deleted successfully
    } else if (banners && banners.length > 0) {
      console.warn(`${banners.length} banners were using the deleted format`);
      // We could notify the user here, but we'll continue since the format was deleted successfully
    }

    console.log(`Successfully deleted format: ${key}`);
    return NextResponse.json({
      message: 'Format deleted successfully',
      format: {
        key: format.key,
        name: format.name
      }
    });
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error(`Error in DELETE /api/formats/${req.url}:`, error);
    return NextResponse.json({
      error: `Internal Server Error: ${errorMessage}`
    }, { status: 500 });
  }
});
