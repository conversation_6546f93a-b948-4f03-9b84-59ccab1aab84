import { NextResponse } from 'next/server';
import { withAuth } from '@/middleware/withAuth';

// GET /api/formats/index - Get all custom formats
export const GET = withAuth(async (req, supabase, user) => {
  const startTime = Date.now();
  try {
    console.log('🕒 [PERF] Fetching custom formats started for user:', user.id);

    // Set up the query with only needed fields
    const { data: customFormats, error } = await supabase
      .from('custom_formats')
      .select('id, name, width, height, key, createdAt')
      .eq('userId', user.id)
      .order('createdAt', { ascending: false });

    const queryTime = Date.now() - startTime;
    console.log(`🕒 [PERF] Supabase query completed in ${queryTime}ms`);

    if (error) {
      console.error('Error fetching custom formats:', error);
      return NextResponse.json({
        error: `Failed to fetch custom formats: ${error.message}`
      }, { status: 500 });
    }

    if (!customFormats || !Array.isArray(customFormats)) {
      console.error('Invalid custom formats data:', customFormats);
      return NextResponse.json({
        error: 'Invalid response from database'
      }, { status: 500 });
    }

    console.log(`Found ${customFormats.length} custom formats for user ${user.id}`);

    // Log the first few formats for debugging
    if (customFormats.length > 0) {
      console.log('Sample format:', JSON.stringify(customFormats[0]));
    }

    const totalTime = Date.now() - startTime;
    console.log(`🕒 [PERF] Custom formats fetch completed in ${totalTime}ms`);

    return NextResponse.json(customFormats);
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('Error in GET /api/formats/index:', error);

    const totalTime = Date.now() - startTime;
    console.log(`🕒 [PERF] Custom formats fetch failed after ${totalTime}ms`);

    return NextResponse.json({
      error: `Failed to fetch custom formats: ${errorMessage}`
    }, { status: 500 });
  }
});
