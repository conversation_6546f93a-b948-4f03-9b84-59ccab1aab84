import { NextResponse } from 'next/server';
import { withAuth } from '@/middleware/withAuth';

export const dynamic = 'force-dynamic';

/**
 * GET /api/formats/count
 * Get the count of custom formats for the authenticated user
 */
export const GET = withAuth(async (req, supabase, user) => {
  try {
    // Count user's custom formats
    const { count, error } = await supabase
      .from('custom_formats')
      .select('id', { count: 'exact', head: true })
      .eq('userId', user.id);

    if (error) {
      console.error('Error counting custom formats:', error);
      return NextResponse.json({ error: 'Failed to count custom formats' }, { status: 500 });
    }

    return NextResponse.json({ count });
  } catch (error) {
    console.error('Error in GET /api/formats/count:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
});
