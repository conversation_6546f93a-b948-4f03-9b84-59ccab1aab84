import { NextResponse } from 'next/server';
import { withAuth } from '@/middleware/withAuth';

export const dynamic = 'force-dynamic';

/**
 * GET /api/projects/count
 * Get the count of projects for the authenticated user
 */
export const GET = withAuth(async (req, supabase, user) => {
  try {
    // Count user's projects
    const { count, error } = await supabase
      .from('projects')
      .select('id', { count: 'exact', head: true })
      .eq('userId', user.id);

    if (error) {
      console.error('Error counting projects:', error);
      return NextResponse.json({ error: 'Failed to count projects' }, { status: 500 });
    }

    return NextResponse.json({ count });
  } catch (error) {
    console.error('Error in GET /api/projects/count:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
});
