import { NextResponse } from 'next/server';
import { createSSRClient } from '@/lib/supabase/server';
import { withAuth } from '@/middleware/withAuth';
import fs from 'fs';
import path from 'path';
import { S3Client, DeleteObjectCommand } from '@aws-sdk/client-s3';

// Initialize S3 client if AWS credentials are available
const s3Client = process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY
  ? new S3Client({
      region: process.env.AWS_REGION || 'us-east-1',
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
      },
    })
  : null;

const UPLOAD_DIR = path.join(process.cwd(), 'public', 'uploads');

async function deleteFromS3(key) {
  if (!s3Client) {
    console.warn('S3 client not initialized, skipping S3 deletion');
    return;
  }

  // Handle null or undefined key
  if (!key) {
    console.warn('No S3 key provided for deletion');
    return;
  }

  try {
    console.log(`Attempting to delete file from S3: ${key}`);

    const command = new DeleteObjectCommand({
      Bucket: process.env.AWS_S3_BUCKET,
      Key: key,
    });

    await s3Client.send(command);
    console.log(`Successfully deleted file from S3: ${key}`);
  } catch (error) {
    console.error(`Error deleting file from S3: ${key}`, error);
    // Not throwing the error so that we can continue with database deletion
  }
}

async function deleteLocal(filepath) {
  try {
    // Handle null or undefined filepath
    if (!filepath) {
      console.warn('No filepath provided for deletion');
      return;
    }

    // Remove leading slash if present
    const cleanPath = filepath.startsWith('/') ? filepath.substring(1) : filepath;
    const fullPath = path.join(process.cwd(), 'public', cleanPath);

    console.log(`Attempting to delete file: ${fullPath}`);

    if (fs.existsSync(fullPath)) {
      await fs.promises.unlink(fullPath);
      console.log(`Successfully deleted local file: ${fullPath}`);
    } else {
      console.warn(`File not found for deletion: ${fullPath}`);
      // Not throwing an error here, as the file might have been deleted already
    }
  } catch (error) {
    console.error(`Error deleting local file: ${filepath}`, error);
    // Not throwing the error so that we can continue with database deletion
  }
}

export const dynamic = 'force-dynamic';

export const DELETE = withAuth(async (req, supabase, user) => {
  // We already have supabase client and user from withAuth middleware
  // Get asset ID from URL
  const id = req.url.split('/').pop();

  try {
    console.log(`Deleting asset with ID: ${id}`);

    if (!id) {
      return NextResponse.json({ error: 'Asset ID is required' }, { status: 400 });
    }

    // 1. Get the asset details first
    const { data: asset, error: fetchError } = await supabase
      .from('assets')
      .select('*')
      .eq('id', id)
      .single();

    if (fetchError) {
      console.error('Error fetching asset:', fetchError);
      return NextResponse.json(
        { error: 'Asset not found', details: fetchError.message },
        { status: 404 }
      );
    }

    if (!asset) {
      return NextResponse.json({ error: 'Asset not found' }, { status: 404 });
    }

    // Check if the asset belongs to the user
    if (asset.userId && asset.userId !== user.id) {
      console.error('Unauthorized access to asset:', { assetId: id, userId: user.id, assetUserId: asset.userId });
      return NextResponse.json({ error: 'You do not have permission to delete this asset' }, { status: 403 });
    }

    console.log('Asset found:', asset);

    // 2. Delete from database FIRST
    console.log('Deleting asset from database:', id);
    const { error: deleteError } = await supabase
      .from('assets')
      .delete()
      .eq('id', id);

    if (deleteError) {
      console.error('Error deleting asset from database:', deleteError);
      return NextResponse.json(
        { error: 'Failed to delete asset from database', details: deleteError.message },
        { status: 500 }
      );
    }

    console.log('Asset deleted successfully from database:', id);

    // 3. Then delete the file from storage
    try {
      if (asset.storageType === 's3' && s3Client) {
        // Extract the key from the URL
        const urlParts = asset.url.split('.amazonaws.com/');
        if (urlParts.length > 1) {
          const key = urlParts[1];
          await deleteFromS3(key);
        } else {
          console.warn('Could not extract S3 key from URL:', asset.url);
        }
      } else {
        // For local storage, the URL will be like /uploads/filename.ext
        await deleteLocal(asset.url);
      }
    } catch (fileError) {
      // Log the error but continue since database deletion was successful
      console.error('Error deleting file from storage:', fileError);
      // We don't return an error here since the database record is already deleted
      // This prevents orphaned database records
    }

    return NextResponse.json({ success: true, message: 'Asset deleted successfully' });
  } catch (error) {
    console.error('Error in asset deletion:', error);
    return NextResponse.json(
      { error: 'Failed to delete asset', details: error.message },
      { status: 500 }
    );
  }
});
