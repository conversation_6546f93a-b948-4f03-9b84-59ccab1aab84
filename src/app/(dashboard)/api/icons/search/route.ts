import { NextResponse } from 'next/server';

const ICONFINDER_API_URL = 'https://api.iconfinder.com/v4';

export const dynamic = 'force-dynamic';
export const runtime = 'nodejs'; // Ensure Node.js runtime

// Fallback icons in case the API is not available
const FALLBACK_ICONS = [
  {
    id: 'fallback-1',
    title: 'Home',
    description: 'Home icon',
    thumbnailUrl: 'https://cdn0.iconfinder.com/data/icons/essentials-solid-glyphs-vol-1/100/Home-House-Property-128.png',
    url: 'https://cdn0.iconfinder.com/data/icons/essentials-solid-glyphs-vol-1/100/Home-House-Property-512.png',
    width: 512,
    height: 512,
    author: 'Vaadin Icons',
    attribution: { name: 'Vaadin', link: 'https://vaadin.com/icons' },
    license: { name: 'MIT', url: null }
  },
  {
    id: 'fallback-2',
    title: 'User',
    description: 'User profile icon',
    thumbnailUrl: 'https://cdn0.iconfinder.com/data/icons/essentials-solid-glyphs-vol-1/100/User-Avatar-Profile-128.png',
    url: 'https://cdn0.iconfinder.com/data/icons/essentials-solid-glyphs-vol-1/100/User-Avatar-Profile-512.png',
    width: 512,
    height: 512,
    author: 'Vaadin Icons',
    attribution: { name: 'Vaadin', link: 'https://vaadin.com/icons' },
    license: { name: 'MIT', url: null }
  },
  {
    id: 'fallback-3',
    title: 'Search',
    description: 'Search magnifying glass',
    thumbnailUrl: 'https://cdn0.iconfinder.com/data/icons/essentials-solid-glyphs-vol-1/100/Search-Magnifying-Glass-128.png',
    url: 'https://cdn0.iconfinder.com/data/icons/essentials-solid-glyphs-vol-1/100/Search-Magnifying-Glass-512.png',
    width: 512,
    height: 512,
    author: 'Vaadin Icons',
    attribution: { name: 'Vaadin', link: 'https://vaadin.com/icons' },
    license: { name: 'MIT', url: null }
  }
]

// Additional fallback icons can be added to the array above as needed

export async function GET(request) {
  // Set content type header explicitly
  const headers = {
    'Content-Type': 'application/json',
    'Cache-Control': 'no-store, max-age=0'
  };

  try {
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('query');

    console.log(`Icon search request received for query: ${query}`);

    // Validate query
    if (!query) {
      console.log('No query provided, returning error');
      return new NextResponse(
        JSON.stringify({ error: 'Search query is required' }),
        { status: 400, headers }
      );
    }

    // Validate API key
    const apiKey = process.env.ICONFINDER_API_KEY;
    if (!apiKey) {
      console.error('Iconfinder API key is missing. Please add ICONFINDER_API_KEY to your .env file');

      // Return fallback icons instead of error
      console.log('Using fallback icons due to missing API key');
      return new NextResponse(
        JSON.stringify({
          icons: FALLBACK_ICONS.filter(icon =>
            icon.title.toLowerCase().includes(query.toLowerCase()) ||
            icon.description.toLowerCase().includes(query.toLowerCase())
          ),
          source: 'fallback'
        }),
        { status: 200, headers }
      );
    }
    // Build query parameters
    const params = new URLSearchParams();
    params.append('query', query);
    params.append('count', '30');
    params.append('offset', '0');
    params.append('vector', 'true');

    const requestUrl = `${ICONFINDER_API_URL}/icons/search?${params.toString()}`;

    console.log(`Searching icons with query: ${query}`);

    // Search icons
    const response = await fetch(requestUrl, {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Accept': 'application/json',
      },
      // Add timeout to prevent hanging requests
      signal: AbortSignal.timeout(10000) // 10 second timeout
    }).catch(error => {
      console.error('Fetch error:', error);
      throw new Error(`Network error: ${error.message}`);
    });

    // Check if response is ok
    if (!response.ok) {
      console.error(`API error: ${response.status} ${response.statusText}`);

      // Return fallback icons instead of error
      console.log('Using fallback icons due to API error');
      return new NextResponse(
        JSON.stringify({
          icons: FALLBACK_ICONS.filter(icon =>
            icon.title?.toLowerCase().includes(query.toLowerCase()) ||
            icon.description?.toLowerCase().includes(query.toLowerCase())
          ),
          source: 'fallback',
          error: `API error: ${response.status}`
        }),
        { status: 200, headers }
      );
    }

    // Parse response as JSON
    let data;
    try {
      data = await response.json();
    } catch (error) {
      console.error('JSON parse error:', error);

      // Return fallback icons instead of error
      console.log('Using fallback icons due to JSON parse error');
      return new NextResponse(
        JSON.stringify({
          icons: FALLBACK_ICONS.filter(icon =>
            icon.title?.toLowerCase().includes(query.toLowerCase()) ||
            icon.description?.toLowerCase().includes(query.toLowerCase())
          ),
          source: 'fallback',
          error: 'JSON parse error'
        }),
        { status: 200, headers }
      );
    }

    // Validate response data
    if (!data || !Array.isArray(data.icons)) {
      console.error('Invalid API response format:', data);

      // Return fallback icons instead of error
      console.log('Using fallback icons due to invalid response format');
      return new NextResponse(
        JSON.stringify({
          icons: FALLBACK_ICONS.filter(icon =>
            icon.title?.toLowerCase().includes(query.toLowerCase()) ||
            icon.description?.toLowerCase().includes(query.toLowerCase())
          ),
          source: 'fallback',
          error: 'Invalid API response format'
        }),
        { status: 200, headers }
      );
    }

    // Process icons
    const icons = data.icons
      .filter(icon => !icon.is_premium) // Filter out premium icons
      .map(icon => {
        try {
          // Find the best preview URL
          let previewUrl = null;
          if (icon.raster_sizes && icon.raster_sizes.length > 0) {
            // Sort by size and find a suitable preview
            const sizes = [...icon.raster_sizes].sort((a, b) => b.size_height - a.size_height);
            for (const size of sizes) {
              if (size.formats && size.formats.length > 0 && size.formats[0].preview_url) {
                previewUrl = size.formats[0].preview_url;
                break;
              }
            }
          }

          // Find download URL
          let downloadUrl = null;
          if (icon.vector_sizes && icon.vector_sizes.length > 0) {
            const vectorSize = icon.vector_sizes[0];
            if (vectorSize.formats && vectorSize.formats.length > 0) {
              // Prefer SVG format
              const svgFormat = vectorSize.formats.find(f => f.format === 'svg');
              downloadUrl = svgFormat ? svgFormat.download_url : vectorSize.formats[0].download_url;
            }
          }

          // Skip icons without preview or download URL
          if (!previewUrl || !downloadUrl) {
            return null;
          }

          return {
            id: icon.icon_id,
            title: icon.tags && icon.tags.length > 0 ? icon.tags[0] : 'Icon',
            description: icon.tags ? icon.tags.join(', ') : '',
            thumbnailUrl: previewUrl,
            url: downloadUrl,
            width: icon.vector_sizes?.[0]?.size_width || 100,
            height: icon.vector_sizes?.[0]?.size_height || 100,
            author: icon.iconset?.author?.name || 'Unknown',
            attribution: {
              name: icon.iconset?.author?.name || 'Unknown Author',
              link: icon.iconset?.author?.website_url || null,
            },
            license: {
              name: icon.iconset?.license?.name || 'Unknown License',
              url: icon.iconset?.license?.url || null,
            }
          };
        } catch (error) {
          console.error('Error processing icon:', error);
          return null;
        }
      })
      .filter(icon => icon !== null);

    // If no icons found, use fallback
    if (icons.length === 0) {
      console.log('No icons found, using fallback icons');
      return NextResponse.json({
        icons: FALLBACK_ICONS.filter(icon =>
          icon.title.toLowerCase().includes(query.toLowerCase()) ||
          icon.description.toLowerCase().includes(query.toLowerCase())
        ),
        source: 'fallback'
      }, { headers });
    }

    // Return icons
    return new NextResponse(
      JSON.stringify({ icons, source: 'iconfinder' }),
      { status: 200, headers }
    );
  } catch (error) {
    console.error('Icon search error:', error);

    // Return fallback icons instead of error
    console.log('Using fallback icons due to error:', error.message);
    // Get query from the URL again to ensure it's defined
    const { searchParams } = new URL(request.url);
    const searchQuery = searchParams.get('query') || '';

    return new NextResponse(
      JSON.stringify({
        icons: FALLBACK_ICONS.filter(icon =>
          icon.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          icon.description?.toLowerCase().includes(searchQuery.toLowerCase())
        ),
        source: 'fallback'
      }),
      { status: 200, headers }
    );
  }
}
