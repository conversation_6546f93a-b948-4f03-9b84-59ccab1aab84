import { NextResponse } from 'next/server';

export const dynamic = 'force-dynamic';
export const runtime = 'nodejs'; // Ensure Node.js runtime

export async function GET(request) {
  try {
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const url = searchParams.get('url');

    if (!url) {
      return NextResponse.json({ error: 'URL is required' }, { status: 400 });
    }

    // Validate API key
    const apiKey = process.env.ICONFINDER_API_KEY;
    if (!apiKey) {
      console.error('Iconfinder API key is missing. Please add ICONFINDER_API_KEY to your .env file');
      return NextResponse.json({
        error: 'Configuration error',
        details: 'API key is not configured'
      }, { status: 500 });
    }

    // Get the icon data with proper headers
    const response = await fetch(url, {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Accept': 'application/json, image/svg+xml'
      }
    });

    if (!response.ok) {
      console.error('Failed to download icon:', {
        status: response.status,
        statusText: response.statusText
      });

      // Try to get error details
      let errorDetails = `Status: ${response.status} ${response.statusText}`;
      try {
        const errorText = await response.text();
        console.error('Error response body:', errorText.substring(0, 200));

        // Try to parse as JSON
        try {
          const errorJson = JSON.parse(errorText);
          errorDetails = errorJson.message || errorJson.error || errorDetails;
        } catch (parseError) {
          // Not JSON, use text snippet
          errorDetails = errorText.substring(0, 100) || errorDetails;
        }
      } catch (textError) {
        console.error('Could not read error response text:', textError);
      }

      // Return error with explicit content type
      return new NextResponse(
        JSON.stringify({
          error: 'Failed to download icon',
          details: errorDetails
        }),
        {
          status: 500,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );
    }

    // Get content type
    const contentType = response.headers.get('content-type');

    // Get the icon data
    const iconData = await response.arrayBuffer();

    // Create a new response with the icon data
    const iconResponse = new NextResponse(iconData);

    // Set appropriate headers
    iconResponse.headers.set('Content-Type', contentType || 'image/svg+xml');
    iconResponse.headers.set('Cache-Control', 'public, max-age=31536000'); // Cache for 1 year

    return iconResponse;
  } catch (error) {
    console.error('Icon download error:', error);
    return new NextResponse(
      JSON.stringify({
        error: 'Failed to download icon',
        details: error.message
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
  }
}
