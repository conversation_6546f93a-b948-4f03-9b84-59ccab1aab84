import { NextResponse } from 'next/server';
import { withAuth } from '@/middleware/withAuth';

// GET /api/ai-generations/[id]
export const GET = withAuth(async (req, supabase, user) => {
  try {
    const { id } = req.params;

    const { data: generation, error } = await supabase
      .from('ai_generations')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching generation:', error);
      return NextResponse.json({ error: 'Failed to fetch generation' }, { status: 500 });
    }

    if (!generation) {
      return NextResponse.json({ error: 'Generation not found' }, { status: 404 });
    }

    // Parse JSON fields
    const parsedGeneration = {
      ...generation,
      banner: generation.banner ? JSON.parse(generation.banner) : null,
      metadata: generation.metadata ? JSON.parse(generation.metadata) : null
    };

    return NextResponse.json({ generation: parsedGeneration });
  } catch (error) {
    console.error('Error in GET /api/ai-generations/[id]:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
});