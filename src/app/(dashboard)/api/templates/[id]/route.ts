import { NextResponse } from 'next/server';
import { withAuth } from '@/middleware/withAuth';

export const GET = withAuth(async (req, supabase, user) => {
  try {
    const { params } = req;
    const id = params.id;

    console.log(`GET /api/templates/${id}`);

    // First, get the template data
    const { data, error } = await supabase
      .from('templates')
      .select(`
        *,
        category:template_categories(*),
        subcategories:template_subcategories!templates_subcategories(*)
      `)
      .eq('id', id)
      .single();

    if (error) throw error;

    // Then, get the tags associated with this template
    const { data: templateTags, error: tagsError } = await supabase
      .from('templates_tags')
      .select(`
        tagId,
        tag:template_tags(*)
      `)
      .eq('templateId', id);

    if (tagsError) throw tagsError;

    // Add tags to the template data
    data.tags = templateTags.map(tt => tt.tag);

    if (!data) {
      return NextResponse.json({ error: 'Template not found' }, { status: 404 });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error(`Error in GET /api/templates/${req.params?.id}:`, error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
});

export const PUT = withAuth(async (req, supabase, user) => {
  try {
    const { params } = req;
    const id = params.id;
    const body = await req.json();

    console.log(`PUT /api/templates/${id}`, body);

    // Remove any fields that shouldn't be updated
    delete body.id;
    delete body.createdAt;
    delete body.updatedAt;

    // Handle subcategories and tags separately if present
    const subcategoryIds = body.subcategoryIds;
    const tagIds = body.tagIds;
    const tags = body.tags;
    delete body.subcategoryIds;
    delete body.tagIds;
    delete body.tags;

    // Ensure elements is properly formatted as a string
    if (body.elements) {
      // If elements is an array, stringify it
      if (Array.isArray(body.elements)) {
        body.elements = JSON.stringify(body.elements);
      }
      // If it's already a string, make sure it's valid JSON
      else if (typeof body.elements === 'string') {
        try {
          // Test if it's valid JSON by parsing and re-stringifying
          const parsed = JSON.parse(body.elements);
          body.elements = JSON.stringify(parsed);
        } catch (e) {
          console.error('Invalid elements JSON:', e);
          // If invalid, set to empty array
          body.elements = '[]';
        }
      }
      // If it's neither an array nor a string, set to empty array
      else {
        body.elements = '[]';
      }
    }

    console.log('Updating template with elements:', body.elements);

    // Update the template
    const { data, error } = await supabase
      .from('templates')
      .update(body)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;

    // If subcategoryIds is provided, update the templates_subcategories junction table
    if (subcategoryIds && Array.isArray(subcategoryIds)) {
      // First, delete existing relationships
      const { error: deleteError } = await supabase
        .from('templates_subcategories')
        .delete()
        .eq('templateId', id);

      if (deleteError) throw deleteError;

      // Then, insert new relationships if there are any subcategories
      if (subcategoryIds.length > 0) {
        const { error: insertError } = await supabase
          .from('templates_subcategories')
          .insert(
            subcategoryIds.map(subcategoryId => ({
              templateId: id,
              subcategoryId
            }))
          );

        if (insertError) throw insertError;
      }
    }

    // If tagIds is provided, update the templates_tags junction table
    if (tagIds && Array.isArray(tagIds)) {
      // First, delete existing relationships
      const { error: deleteTagsError } = await supabase
        .from('templates_tags')
        .delete()
        .eq('templateId', id);

      if (deleteTagsError) throw deleteTagsError;

      // Then, insert new relationships if there are any tags
      if (tagIds.length > 0) {
        const { error: insertTagsError } = await supabase
          .from('templates_tags')
          .insert(
            tagIds.map(tagId => ({
              templateId: id,
              tagId
            }))
          );

        if (insertTagsError) throw insertTagsError;
      }
    }

    // Handle new tags if present
    if (tags && Array.isArray(tags) && tags.length > 0) {
      // Create new tags and connect them to the template
      for (const tagName of tags) {
        // Create the tag
        const { data: newTag, error: createTagError } = await supabase
          .from('template_tags')
          .insert({
            name: tagName,
            slug: tagName.toLowerCase().replace(/[^a-z0-9]+/g, '-')
          })
          .select()
          .single();

        if (createTagError) throw createTagError;

        // Connect the tag to the template
        const { error: connectTagError } = await supabase
          .from('templates_tags')
          .insert({
            templateId: id,
            tagId: newTag.id
          });

        if (connectTagError) throw connectTagError;
      }
    }

    // Fetch the updated template with its relationships
    const { data: updatedTemplate, error: fetchError } = await supabase
      .from('templates')
      .select(`
        *,
        category:template_categories(*),
        subcategories:template_subcategories!templates_subcategories(*)
      `)
      .eq('id', id)
      .single();

    if (fetchError) throw fetchError;

    // Then, get the tags associated with this template
    const { data: templateTags, error: tagsError } = await supabase
      .from('templates_tags')
      .select(`
        tagId,
        tag:template_tags(*)
      `)
      .eq('templateId', id);

    if (tagsError) throw tagsError;

    // Add tags to the template data
    updatedTemplate.tags = templateTags.map(tt => tt.tag);

    return NextResponse.json(updatedTemplate);
  } catch (error) {
    console.error(`Error in PUT /api/templates/${req.params?.id}:`, error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}, 'manage_templates');

export const DELETE = withAuth(async (req, supabase, user) => {
  try {
    const { params } = req;
    const id = params.id;

    console.log(`DELETE /api/templates/${id}`);

    const { error } = await supabase
      .from('templates')
      .delete()
      .eq('id', id);

    if (error) throw error;

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error(`Error in DELETE /api/templates/${req.params?.id}:`, error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}, 'manage_templates');
