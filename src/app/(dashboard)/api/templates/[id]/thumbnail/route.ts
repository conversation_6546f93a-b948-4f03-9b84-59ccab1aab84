import { NextResponse } from 'next/server';
import { withAuth } from '@/middleware/withAuth';
import fs from 'fs';
import path from 'path';
import { createId } from '@paralleldrive/cuid2';

/**
 * Processes a template thumbnail asynchronously
 * This function is not awaited by the main request handler
 */
async function processThumbnailAsync(buffer, id, supabase) {
  console.log(`Starting async thumbnail processing for template ${id}`);
  try {
    // Generate a unique filename
    const filename = `template_${id}_${Date.now()}.png`;

    // Try to upload to Supabase Storage first
    let thumbnailUrl;
    try {
      // Upload the thumbnail to Supabase Storage
      const { data: uploadData, error: uploadError } = await supabase
        .storage
        .from('thumbnails')
        .upload(filename, buffer, {
          contentType: 'image/png',
          upsert: true
        });

      if (uploadError) {
        // If bucket not found or other storage error, throw to trigger fallback
        if (uploadError.error === 'Bucket not found' || uploadError.statusCode === '404' || uploadError.message === 'Bucket not found') {
          console.log('Bucket not found, using local storage fallback');
          throw new Error('Bucket not found, using local storage fallback');
        }
        console.error('Error uploading template thumbnail to storage:', uploadError);
        return;
      }

      // Get the public URL
      const { data: urlData } = await supabase
        .storage
        .from('thumbnails')
        .getPublicUrl(filename);

      thumbnailUrl = urlData.publicUrl;
    } catch (storageError) {
      console.log('Using local storage fallback for template thumbnail:', storageError.message);

      // Fallback to local file storage
      try {
        // Ensure uploads directory exists
        const uploadsDir = path.join(process.cwd(), 'public', 'uploads');
        if (!fs.existsSync(uploadsDir)) {
          fs.mkdirSync(uploadsDir, { recursive: true });
        }

        // Generate a unique filename
        const localFilename = `template_${id}_${createId()}.png`;
        const filePath = path.join(uploadsDir, localFilename);

        // Write the file
        await fs.promises.writeFile(filePath, buffer);

        // Set the URL to the local path
        thumbnailUrl = `/uploads/${localFilename}`;
      } catch (localStorageError) {
        console.error('Error saving template thumbnail to local storage:', localStorageError);
        return;
      }
    }

    // Update the template with the thumbnail URL
    const { data, error } = await supabase
      .from('templates')
      .update({ thumbnail: thumbnailUrl })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating template with thumbnail URL:', error);
      return;
    }

    console.log(`Async template thumbnail processing completed for template ${id}`);
  } catch (error) {
    console.error(`Error in async template thumbnail processing for template ${id}:`, error);
  }
}

export const POST = withAuth(async (req, supabase, user) => {
  try {
    const { params } = req;
    const id = params.id;

    // Get the form data
    const formData = await req.formData();
    const thumbnail = formData.get('thumbnail');

    if (!thumbnail) {
      return NextResponse.json({ error: 'No thumbnail provided' }, { status: 400 });
    }

    // Convert the file to a buffer
    const buffer = Buffer.from(await thumbnail.arrayBuffer());

    // Check if this is a non-blocking request (client doesn't need to wait for processing)
    const nonBlocking = formData.get('nonBlocking') === 'true';

    if (nonBlocking) {
      // Start processing in the background without awaiting
      // This allows the request to return immediately
      processThumbnailAsync(buffer, id, supabase).catch(error => {
        console.error('Unhandled error in background template thumbnail processing:', error);
      });

      // Return success immediately
      return NextResponse.json({
        success: true,
        message: 'Template thumbnail processing started in background',
        thumbnailPending: true
      });
    } else {
      // For backward compatibility, process synchronously if nonBlocking is not specified
      // Generate a unique filename
      const filename = `template_${id}_${Date.now()}.png`;

      // Upload the thumbnail to Supabase Storage
      const { data: uploadData, error: uploadError } = await supabase
        .storage
        .from('thumbnails')
        .upload(filename, buffer, {
          contentType: 'image/png',
          upsert: true
        });

      if (uploadError) {
        console.error('Error uploading template thumbnail to storage:', uploadError);
        return NextResponse.json({ error: 'Failed to upload thumbnail' }, { status: 500 });
      }

      // Get the public URL
      const { data: urlData } = await supabase
        .storage
        .from('thumbnails')
        .getPublicUrl(filename);

      const thumbnailUrl = urlData.publicUrl;

      // Update the template with the thumbnail URL
      const { data, error } = await supabase
        .from('templates')
        .update({ thumbnail: thumbnailUrl })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Error updating template with thumbnail URL:', error);
        return NextResponse.json({ error: 'Failed to update template' }, { status: 500 });
      }

      return NextResponse.json({
        success: true,
        thumbnail: thumbnailUrl,
        template: data
      });
    }
  } catch (error) {
    console.error('Error in POST /api/templates/[id]/thumbnail:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
});
