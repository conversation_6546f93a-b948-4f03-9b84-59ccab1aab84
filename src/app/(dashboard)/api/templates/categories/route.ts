import { NextResponse } from 'next/server';
import { withAuth } from '@/middleware/withAuth';

export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;

export const GET = withAuth(async (req, supabase, user) => {
  try {
    const { data, error } = await supabase
      .from('template_categories')
      .select('*')
      .order('name');

    if (error) throw error;

    return NextResponse.json(data || []);
  } catch (error) {
    console.error('Error in GET /api/templates/categories:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
});
