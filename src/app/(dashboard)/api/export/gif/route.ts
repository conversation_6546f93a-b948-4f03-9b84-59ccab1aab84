import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/middleware/withAuth';
import { exportAsGIF } from '@/utils/export/remotionExport';
import path from 'path';
import fs from 'fs';

export const POST = withAuth(async (req: NextRequest, supabase, user) => {
  try {
    const body = await req.json();
    const {
      bannerId,
      width,
      height,
      elements,
      backgroundColor,
      totalBannerPlaybackDurationInSeconds,
      isLoopEnabled = true, // GIFs typically loop by default
      animationSpeed = 1,
      fps = 15, // Lower FPS for GIF to reduce file size
      bannerName = 'banner'
    } = body;

    if (!bannerId || !width || !height || !elements) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    // Verify banner ownership
    const { data: banner, error: bannerError } = await supabase
      .from('banners')
      .select('userId')
      .eq('id', bannerId)
      .single();

    if (bannerError || !banner) {
      return NextResponse.json({ error: 'Banner not found' }, { status: 404 });
    }

    if (banner.userId !== user.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Create output directory and file path
    const outputDir = path.join(process.cwd(), 'public', 'exports', user.id);
    const sanitizedName = bannerName.replace(/[^a-z0-9]/gi, '_').toLowerCase();
    const fileName = `${sanitizedName}_${bannerId}_${Date.now()}.gif`;
    const outputPath = path.join(outputDir, fileName);

    // Ensure directory exists
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // Export the banner as GIF
    await exportAsGIF({
      width,
      height,
      elements,
      backgroundColor,
      totalBannerPlaybackDurationInSeconds,
      isLoopEnabled,
      animationSpeed,
      fps,
      format: 'gif',
      outputPath,
    });

    // Generate download URL
    const downloadUrl = `/exports/${user.id}/${fileName}`;

    // Update banner record with export info
    await supabase
      .from('banners')
      .update({
        lastExportedAt: new Date().toISOString(),
        lastExportFormat: 'gif',
        updatedAt: new Date().toISOString()
      })
      .eq('id', bannerId);

    return NextResponse.json({
      success: true,
      downloadUrl,
      fileName,
      format: 'gif'
    });

  } catch (error) {
    console.error('Error exporting GIF:', error);
    return NextResponse.json(
      { error: 'Failed to export GIF', details: error.message },
      { status: 500 }
    );
  }
});

export const GET = async () => {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
};
