import { NextResponse } from 'next/server';

const UNSPLASH_API_URL = 'https://api.unsplash.com/search/photos';
const PEXELS_API_URL = 'https://api.pexels.com/v1/search';

async function searchUnsplash(query) {
  const response = await fetch(
    `${UNSPLASH_API_URL}?query=${encodeURIComponent(query)}&per_page=20`,
    {
      headers: {
        'Authorization': `Client-ID ${process.env.UNSPLASH_ACCESS_KEY}`
      }
    }
  );
  
  if (!response.ok) {
    const errorText = await response.text();
    console.error('Unsplash API error:', {
      status: response.status,
      body: errorText
    });
    throw new Error(`Unsplash API error: ${response.status}`);
  }

  const data = await response.json();
  
  if (!data.results) {
    console.error('Unexpected Unsplash response format:', data);
    throw new Error('Invalid response format from Unsplash');
  }

  return data.results.map(image => ({
    id: image.id,
    provider: 'unsplash',
    thumbnailUrl: image.urls.small,
    url: image.urls.regular,
    width: image.width,
    height: image.height,
    description: image.description || image.alt_description,
    attribution: {
      name: image.user.name,
      username: image.user.username,
      link: image.user.links.html
    }
  }));
}

async function searchPexels(query) {
  const response = await fetch(
    `${PEXELS_API_URL}?query=${encodeURIComponent(query)}&per_page=20`,
    {
      headers: {
        'Authorization': process.env.PEXELS_API_KEY
      }
    }
  );
  
  if (!response.ok) {
    const errorText = await response.text();
    console.error('Pexels API error:', {
      status: response.status,
      body: errorText
    });
    throw new Error(`Pexels API error: ${response.status}`);
  }

  const data = await response.json();
  
  if (!data.photos) {
    console.error('Unexpected Pexels response format:', data);
    throw new Error('Invalid response format from Pexels');
  }

  return data.photos.map(photo => ({
    id: photo.id,
    provider: 'pexels',
    thumbnailUrl: photo.src.medium,
    url: photo.src.large2x,
    width: photo.width,
    height: photo.height,
    description: photo.alt || photo.photographer,
    attribution: {
      name: photo.photographer,
      link: photo.photographer_url
    }
  }));
}

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q');
    const provider = searchParams.get('provider');

    if (!query) {
      return NextResponse.json(
        { error: 'Search query is required' },
        { status: 400 }
      );
    }

    // Validate API keys
    if (provider === 'unsplash' && !process.env.UNSPLASH_ACCESS_KEY) {
      return NextResponse.json(
        { 
          error: 'Configuration error',
          details: 'Unsplash API key is not configured. Please add UNSPLASH_ACCESS_KEY to your .env file'
        },
        { status: 500 }
      );
    }

    if (provider === 'pexels' && !process.env.PEXELS_API_KEY) {
      return NextResponse.json(
        { 
          error: 'Configuration error',
          details: 'Pexels API key is not configured. Please add PEXELS_API_KEY to your .env file'
        },
        { status: 500 }
      );
    }

    let images = [];

    switch (provider) {
      case 'unsplash':
        images = await searchUnsplash(query);
        break;
      case 'pexels':
        images = await searchPexels(query);
        break;
      default:
        return NextResponse.json(
          { error: 'Invalid provider' },
          { status: 400 }
        );
    }

    return NextResponse.json({ images });
  } catch (error) {
    console.error('Stock image search error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to search stock images',
        details: error.message
      },
      { status: 500 }
    );
  }
} 