import { NextResponse } from 'next/server';
import { withAuth } from '@/middleware/withAuth';

// GET /api/ai-usage
export const GET = withAuth(async (_req: Request, supabase: any, user: any) => {
  try {
    // Call the get_ai_usage function to get the user's AI usage
    const { data, error } = await supabase.rpc('get_ai_usage', {
      user_id: user.id
    });

    if (error) {
      console.error('Error fetching AI usage:', error);
      return NextResponse.json({ error: 'Failed to fetch AI usage' }, { status: 500 });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in GET /api/ai-usage:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
});
