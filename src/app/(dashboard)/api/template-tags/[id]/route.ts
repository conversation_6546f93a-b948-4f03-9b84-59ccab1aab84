import { NextResponse } from 'next/server';
import { withAuth } from '@/middleware/withAuth';
import slugify from 'slugify';

// Helper function to generate a slug from a name
function generateSlug(name) {
  return slugify(name, { lower: true });
}

// GET /api/template-tags/[id] - Get a single tag
export const GET = withAuth(async (req, supabase, user) => {
  try {
    const { id } = req.params;

    // Get tag with template count
    const { data: tag, error } = await supabase
      .from('template_tags')
      .select(`
        id,
        name,
        description,
        slug,
        templates_tags(count)
      `)
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching tag:', error);
      return NextResponse.json({ error: 'Failed to fetch tag' }, { status: 500 });
    }

    if (!tag) {
      return NextResponse.json({ error: 'Tag not found' }, { status: 404 });
    }

    // Format the response to convert the templates_count object to a number
    const formattedTag = {
      ...tag,
      templates_count: tag.templates_tags.length > 0 ? tag.templates_tags[0].count : 0,
      templates_tags: undefined // Remove the original property
    };

    return NextResponse.json(formattedTag);
  } catch (error) {
    console.error('Error in GET /api/template-tags/[id]:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
});

// PUT /api/template-tags/[id] - Update a tag
export const PUT = withAuth(async (req, supabase, user) => {
  try {
    const body = await req.json();
    const { id } = req.params;

    // Validate request body
    if (!body.name?.trim()) {
      return NextResponse.json({ error: 'Name is required' }, { status: 400 });
    }

    // Generate a new slug if the name has changed
    // First, get the current tag to check if name has changed
    const { data: currentTag, error: fetchError } = await supabase
      .from('template_tags')
      .select('name')
      .eq('id', id)
      .single();

    if (fetchError) {
      console.error('Error fetching tag:', fetchError);
      return NextResponse.json({ error: 'Failed to fetch tag' }, { status: 500 });
    }

    // Prepare update data
    const updateData = {
      name: body.name.trim(),
      description: body.description?.trim() || null
    };

    // If name has changed, generate a new slug
    if (currentTag && currentTag.name !== body.name.trim()) {
      const slug = generateSlug(body.name.trim());

      // Check if slug already exists for a different tag
      const { data: existingTag, error: slugCheckError } = await supabase
        .from('template_tags')
        .select('id')
        .eq('slug', slug)
        .neq('id', id)
        .maybeSingle();

      if (slugCheckError) {
        console.error('Error checking slug uniqueness:', slugCheckError);
        return NextResponse.json({ error: 'Error checking slug uniqueness' }, { status: 500 });
      }

      // If slug exists for a different tag, make it unique
      if (updateData as any) {
        (updateData as any).slug = existingTag ? `${slug}-${Date.now()}` : slug;
      }
    }

    // Update tag
    const { data: tag, error } = await supabase
      .from('template_tags')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating tag:', error);
      return NextResponse.json({ error: 'Failed to update tag' }, { status: 500 });
    }

    if (!tag) {
      return NextResponse.json({ error: 'Tag not found' }, { status: 404 });
    }

    return NextResponse.json({ tag });
  } catch (error) {
    console.error('Error in PUT /api/template-tags/[id]:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
});

// DELETE /api/template-tags/[id] - Delete a tag
export const DELETE = withAuth(async (req, supabase, user) => {
  try {
    const { id } = req.params;

    // Delete tag
    const { error } = await supabase
      .from('template_tags')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting tag:', error);
      return NextResponse.json({ error: 'Failed to delete tag' }, { status: 500 });
    }

    return new NextResponse(null, { status: 204 });
  } catch (error) {
    console.error('Error in DELETE /api/template-tags/[id]:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
});