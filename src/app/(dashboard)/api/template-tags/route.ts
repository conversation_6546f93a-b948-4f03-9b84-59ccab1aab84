import { NextResponse } from 'next/server';
import { withAuth } from '@/middleware/withAuth';
import slugify from 'slugify';

// GET /api/template-tags - Get all tags
export const GET = withAuth(async (req, supabase, user) => {
  try {
    // Get all tags with template count - only select necessary fields
    // Count templates through the conjunction table
    const { data: tags, error } = await supabase
      .from('template_tags')
      .select(`
        id,
        name,
        description,
        slug,
        templates_tags(count)
      `)
      .order('name');

    if (error) {
      console.error('Error fetching tags:', error);
      return NextResponse.json({ error: 'Failed to fetch tags' }, { status: 500 });
    }

    // Format the response to convert the templates_count object to a number
    const formattedTags = tags.map(tag => ({
      ...tag,
      templates_count: tag.templates_tags.length > 0 ? tag.templates_tags[0].count : 0,
      templates_tags: undefined // Remove the original property
    }));

    // Return the formatted tags
    return NextResponse.json(formattedTags);
  } catch (error) {
    console.error('Error in GET /api/template-tags:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
});

// Helper function to generate a slug from a name
function generateSlug(name) {
  return slugify(name, { lower: true });
}

// POST /api/template-tags - Create a new tag
export const POST = withAuth(async (req, supabase, user) => {
  try {
    const body = await req.json();

    // Validate request body
    if (!body.name?.trim()) {
      return NextResponse.json({ error: 'Name is required' }, { status: 400 });
    }

    // Generate a slug from the name
    const name = body.name.trim();
    const slug = generateSlug(name);

    // Check if slug already exists
    const { data: existingTag, error: slugCheckError } = await supabase
      .from('template_tags')
      .select('id')
      .eq('slug', slug)
      .maybeSingle();

    if (slugCheckError) {
      console.error('Error checking slug uniqueness:', slugCheckError);
      return NextResponse.json({ error: 'Error checking slug uniqueness' }, { status: 500 });
    }

    // If slug exists, make it unique
    const uniqueSlug = existingTag ? `${slug}-${Date.now()}` : slug;

    // Create new tag
    const { data: tag, error } = await supabase
      .from('template_tags')
      .insert([{
        name: name,
        description: body.description?.trim() || null,
        slug: uniqueSlug
      }])
      .select()
      .single();

    if (error) {
      console.error('Error creating tag:', error);
      return NextResponse.json({ error: 'Failed to create tag' }, { status: 500 });
    }

    return NextResponse.json({ tag }, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/template-tags:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
});