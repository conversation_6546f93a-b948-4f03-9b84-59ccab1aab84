import { NextResponse } from 'next/server';
import { createSSRClient } from '@/lib/supabase/server';
import type { EmailOtpType } from '@supabase/supabase-js';

export const dynamic = 'force-dynamic';

/**
 * Auth confirm route for handling email verification and password reset
 * This route verifies the OTP token and redirects to the appropriate page
 */
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const token_hash = searchParams.get('token_hash');
    const typeParam = searchParams.get('type');
    const nextParam = searchParams.get('next');
    // Only allow same-origin relative paths, disallow protocol or double-slash prefixes
    const isSafePath =
      nextParam &&
      nextParam.startsWith('/') &&
      !nextParam.startsWith('//') &&
      !nextParam.includes('://');
    const next = isSafePath ? nextParam : '/dashboard';

    console.log('Auth confirm: Processing verification with type:', typeParam);

    if (!token_hash || !typeParam) {
      console.error('Auth confirm: Missing token_hash or type');
      return NextResponse.redirect(
        new URL('/error?message=Invalid+verification+link', request.url)
      );
    }

    // Convert string type to EmailOtpType
    const type = typeParam as EmailOtpType;

    const supabase = await createSSRClient();
    const { error } = await supabase.auth.verifyOtp({
      type,
      token_hash,
    });

    if (error) {
      console.error('Auth confirm: Error verifying OTP:', error);
      return NextResponse.redirect(
        new URL(`/error?message=${encodeURIComponent(error.message)}`, request.url)
      );
    }

    console.log('Auth confirm: Successfully verified OTP, redirecting to:', next);

    // Redirect user to specified redirect URL or dashboard
    return NextResponse.redirect(new URL(next, request.url));
  } catch (error) {
    console.error('Auth confirm error:', error);
    return NextResponse.redirect(
      new URL('/error?message=Verification+failed', request.url)
    );
  }
}
