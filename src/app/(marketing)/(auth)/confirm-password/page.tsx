import { redirect } from 'next/navigation';
import { createSSRClient } from '@/lib/supabase/server';
import ConfirmPasswordPageClient from '@/components/(marketing)/auth/ConfirmPasswordPageClient';

// Server component for password reset confirmation
export default async function ConfirmPasswordPage() {
  // Render the client component that handles the password reset
  return <ConfirmPasswordPageClient />;
}
