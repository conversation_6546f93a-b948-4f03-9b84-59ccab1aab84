import { redirect } from 'next/navigation';
import { createSSRClient } from '@/lib/supabase/server';
import RegisterPageClient from '@/components/(marketing)/auth/RegisterPageClient';

// Server component that checks auth status and renders the client component
export default async function RegisterPage() {
  // Create server-side Supabase client
  const supabase = await createSSRClient();

  // Check if user is authenticated
  const { data: { user } } = await supabase.auth.getUser();

  // If user is logged in, redirect to dashboard
  if (user) {
    redirect('/dashboard');
  }

  // If not logged in, render the client component
  return <RegisterPageClient />;
}