'use client';

import React, { ReactNode } from 'react';
import { ERROR_TYPES, ERROR_MESSAGES } from '@/services/aiStudioService';

interface AIStudioErrorBoundaryProps {
  children: ReactNode;
  onReset?: () => void;
}

interface AIStudioErrorBoundaryState {
  hasError: boolean;
  error: any;
  errorInfo: React.ErrorInfo | null;
}

/**
 * AI Studio Error Boundary Component
 * Provides user-friendly error messages and recovery options
 */
class AIStudioErrorBoundary extends React.Component<AIStudioErrorBoundaryProps, AIStudioErrorBoundaryState> {
  constructor(props: AIStudioErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error: any): Partial<AIStudioErrorBoundaryState> {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error
    };
  }

  componentDidCatch(error: any, errorInfo: React.ErrorInfo): void {
    // Log the error to console
    console.error('AI Studio Error:', error, errorInfo);
    this.setState({ errorInfo });
  }

  // Get a user-friendly error message based on the error type
  getUserFriendlyMessage(): string {
    const { error } = this.state;

    if (!error) return ERROR_MESSAGES[ERROR_TYPES.UNKNOWN];

    // If it's our custom error with a type
    if (error.type && ERROR_MESSAGES[error.type]) {
      return ERROR_MESSAGES[error.type];
    }

    // Check for specific error messages
    const errorMessage = error.message || '';

    if (errorMessage.includes('network') || errorMessage.includes('fetch')) {
      return ERROR_MESSAGES[ERROR_TYPES.NETWORK];
    }

    if (errorMessage.includes('timeout')) {
      return ERROR_MESSAGES[ERROR_TYPES.TIMEOUT];
    }

    if (errorMessage.includes('auth')) {
      return ERROR_MESSAGES[ERROR_TYPES.AUTHENTICATION];
    }

    // Default to the original error message or unknown error
    return error.message || ERROR_MESSAGES[ERROR_TYPES.UNKNOWN];
  }

  render() {
    if (this.state.hasError) {
      const errorMessage = this.getUserFriendlyMessage();

      return (
        <div className="h-full flex flex-col items-center justify-center p-6 bg-gray-50">
          <div className="w-16 h-16 mb-4 text-red-500">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Something went wrong</h3>
          <p className="text-sm text-gray-600 text-center mb-6">{errorMessage}</p>
          <button
            onClick={() => this.setState({ hasError: false, error: null, errorInfo: null })}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
          {this.props.onReset && (
            <button
              onClick={this.props.onReset}
              className="mt-2 px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors"
            >
              Reset Panel
            </button>
          )}
        </div>
      );
    }

    return this.props.children;
  }
}

export default AIStudioErrorBoundary;
