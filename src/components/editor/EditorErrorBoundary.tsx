'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import ErrorBoundary from '@/components/common/ErrorBoundary';
import { Button } from '@/components/ui/button';

interface EditorErrorBoundaryProps {
  children: React.ReactNode;
  projectId?: string;
  bannerId?: string;
}

const EditorErrorFallback = ({ 
  projectId, 
  bannerId 
}: { 
  projectId?: string; 
  bannerId?: string; 
}) => {
  const router = useRouter();

  const handleGoToDashboard = () => {
    router.push('/dashboard');
  };

  const handleRetryEditor = () => {
    if (bannerId && projectId) {
      router.push(`/editor?projectId=${projectId}&bannerId=${bannerId}`);
    } else if (projectId) {
      router.push(`/editor?projectId=${projectId}`);
    } else {
      window.location.reload();
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100">
      <div className="bg-white shadow-lg rounded-lg p-8 max-w-md w-full mx-4">
        <div className="text-red-500 mb-6 flex justify-center">
          <svg 
            className="h-16 w-16" 
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={1.5} 
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" 
            />
          </svg>
        </div>
        
        <h2 className="text-2xl font-semibold text-center mb-4 text-gray-900">
          Editor Error
        </h2>
        
        <p className="text-gray-600 text-center mb-8">
          The banner editor encountered an unexpected error. Your work might be saved automatically, 
          but you may need to restart the editor.
        </p>
        
        <div className="flex flex-col gap-3">
          <Button 
            onClick={handleRetryEditor}
            className="w-full bg-blue-600 hover:bg-blue-700"
          >
            <svg 
              className="h-4 w-4 mr-2" 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" 
              />
            </svg>
            Restart Editor
          </Button>
          
          <Button 
            variant="outline"
            onClick={handleGoToDashboard}
            className="w-full"
          >
            <svg 
              className="h-4 w-4 mr-2" 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M3 12l2-2m0 0l7-7 7 7m-7-7v18" 
              />
            </svg>
            Back to Dashboard
          </Button>
        </div>
        
        <div className="mt-6 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
          <p className="text-xs text-yellow-800">
            <strong>Tip:</strong> Your banner data is automatically saved every few seconds. 
            If you were working on a banner, it should be recovered when you restart the editor.
          </p>
        </div>
      </div>
    </div>
  );
};

const EditorErrorBoundary: React.FC<EditorErrorBoundaryProps> = ({ 
  children, 
  projectId, 
  bannerId 
}) => {
  const handleError = (error: Error, errorInfo: React.ErrorInfo) => {
    // Log error for debugging (in production, this could send to error reporting service)
    console.error('Editor Error:', {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      projectId,
      bannerId,
      timestamp: new Date().toISOString()
    });

    // In production, you might want to send this to an error reporting service
    // like Sentry, LogRocket, or Bugsnag
    if (process.env.NODE_ENV === 'production') {
      // Example: Sentry.captureException(error, { contexts: { editor: { projectId, bannerId } } });
    }
  };

  return (
    <ErrorBoundary
      fallback={<EditorErrorFallback projectId={projectId} bannerId={bannerId} />}
      onError={handleError}
    >
      {children}
    </ErrorBoundary>
  );
};

export default EditorErrorBoundary;