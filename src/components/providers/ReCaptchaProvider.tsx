'use client';

import { ReCaptchaProvider as NextReCaptchaProvider } from 'next-recaptcha-v3';
import { ReactNode } from 'react';

interface ReCaptchaProviderProps {
  children: ReactNode;
}

export function ReCaptchaProvider({ children }: ReCaptchaProviderProps) {
  // Get the reCAPTCHA site key from environment variables
  const reCaptchaSiteKey = process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY || '6LcZtjUrAAAAAF9sN4vXNGpBw8_UvNoK-08LeEjM';

  return (
    <NextReCaptchaProvider
      reCaptchaKey={reCaptchaSiteKey}
      language="en"
      useEnterprise={false}
      useRecaptchaNet={false}
    >
      {children}
    </NextReCaptchaProvider>
  );
}
