'use client';

import { HomeLayout } from '@/components/(marketing)/layout/HomeLayout';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { motion } from 'framer-motion';
import {
  HiArrowRight,
  HiCode,
  HiGlobe,
  HiLightBulb,
  HiUsers,
  HiColorSwatch,
  HiClock,
  HiDownload
} from 'react-icons/hi';

export function AboutPage() {
  return (
    <HomeLayout>
      {/* Hero Section */}
      <div className="bg-white">
        <div className="mx-auto max-w-7xl px-6 lg:px-8 py-20 sm:py-28">
          <div className="mx-auto max-w-2xl text-center">
            <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl">
              About Banner.so
            </h1>
            <p className="mt-6 text-lg leading-8 text-gray-600">
              We're on a mission to make HTML5 banner creation accessible to everyone,
              regardless of technical skill or design experience.
            </p>
          </div>
        </div>
      </div>

      {/* Our Story Section */}
      <div className="bg-gray-50">
        <div className="mx-auto max-w-7xl px-6 lg:px-8 py-20 sm:py-28">
          <div className="mx-auto max-w-3xl">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl text-center mb-12">
              Our Story
            </h2>
            <div className="space-y-10 text-lg text-gray-600 leading-relaxed">
              <p>
                Banner.so was born out of frustration with the existing tools for creating HTML5 animated banners.
                We saw marketers and designers struggling with complex software, coding requirements, and high costs
                just to create simple animated ads.
              </p>
              <p>
                We believed there had to be a better way. In 2025, we set out to build a platform that would make
                banner creation simple, fast, and accessible to everyone. Our goal was to eliminate the technical
                barriers and empower marketers and designers to create professional-quality animated banners without
                any coding knowledge.
              </p>
              <p>
                Today, Banner.so is helping thousands of marketers and designers create stunning HTML5 banners
                in minutes instead of hours. Our intuitive drag-and-drop editor, extensive template library, and
                powerful animation tools have revolutionized the way teams approach banner creation.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Our Mission & Values */}
      <div className="bg-white">
        <div className="mx-auto max-w-7xl px-6 lg:px-8 py-20 sm:py-28">
          <div className="mx-auto max-w-2xl lg:max-w-none">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                Our Mission & Values
              </h2>
              <p className="mt-6 text-lg text-gray-600 max-w-3xl mx-auto">
                At Banner.so, we're guided by a clear mission: to democratize banner creation and empower everyone to create professional-quality animated banners without technical barriers.
              </p>
            </div>

            <div className="grid grid-cols-1 gap-x-16 gap-y-14 lg:grid-cols-3 mt-16">
              <motion.div
                className="flex flex-col"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5 }}
              >
                <div className="rounded-full bg-blue-500/10 p-3 w-14 h-14 flex items-center justify-center mb-6">
                  <HiLightBulb className="h-7 w-7 text-blue-500" />
                </div>
                <h3 className="text-xl font-semibold leading-8 text-gray-900 mb-4">Simplicity First</h3>
                <p className="text-base leading-7 text-gray-600">
                  We believe powerful tools don't have to be complicated. We're constantly working to make banner
                  creation as simple and intuitive as possible, removing unnecessary complexity at every step.
                </p>
              </motion.div>

              <motion.div
                className="flex flex-col"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.1 }}
              >
                <div className="rounded-full bg-blue-500/10 p-3 w-14 h-14 flex items-center justify-center mb-6">
                  <HiUsers className="h-7 w-7 text-blue-500" />
                </div>
                <h3 className="text-xl font-semibold leading-8 text-gray-900 mb-4">Customer-Driven</h3>
                <p className="text-base leading-7 text-gray-600">
                  Our customers' needs drive our product development. We listen closely to feedback and continuously
                  improve our platform based on real user experiences and challenges they face.
                </p>
              </motion.div>

              <motion.div
                className="flex flex-col"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                <div className="rounded-full bg-blue-500/10 p-3 w-14 h-14 flex items-center justify-center mb-6">
                  <HiGlobe className="h-7 w-7 text-blue-500" />
                </div>
                <h3 className="text-xl font-semibold leading-8 text-gray-900 mb-4">Accessibility</h3>
                <p className="text-base leading-7 text-gray-600">
                  We're committed to making professional banner creation accessible to everyone, regardless of
                  technical skill, design experience, or budget constraints.
                </p>
              </motion.div>
            </div>
          </div>
        </div>
      </div>

      {/* Why Choose Banner.so */}
      <div className="bg-gray-50">
        <div className="mx-auto max-w-7xl px-6 lg:px-8 py-20 sm:py-28">
          <div className="mx-auto max-w-2xl lg:max-w-none">
            <div className="text-center">
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                Why Choose Banner.so
              </h2>
              <p className="mt-6 text-lg text-gray-600 max-w-3xl mx-auto">
                What sets us apart from other banner creation tools
              </p>
            </div>

            <div className="mt-16 flex justify-center mb-16">
              <motion.div
                className="relative rounded-xl overflow-hidden shadow-xl max-w-5xl"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.7 }}
              >
                <img
                  src="/images/svg/editor-preview.svg"
                  alt="Banner.so Editor Interface"
                  className="w-full h-auto"
                />
                <div className="absolute inset-0 ring-1 ring-inset ring-black/10 rounded-xl"></div>
              </motion.div>
            </div>

            <div className="grid grid-cols-1 gap-x-16 gap-y-14 lg:grid-cols-2 mt-16">
              <motion.div
                className="flex flex-col gap-6 sm:flex-row lg:flex-col"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5 }}
              >
                <div className="flex h-14 w-14 items-center justify-center rounded-xl bg-blue-500 text-white sm:shrink-0">
                  <HiCode className="h-8 w-8" />
                </div>
                <div className="sm:min-w-0 sm:flex-1">
                  <h3 className="text-xl font-semibold leading-8 text-gray-900 mb-3">No Coding Required</h3>
                  <p className="text-base leading-7 text-gray-600">
                    Our intuitive drag-and-drop editor lets you create professional HTML5 banners without writing a
                    single line of code. Just select elements, customize, and animate.
                  </p>
                </div>
              </motion.div>

              <motion.div
                className="flex flex-col gap-6 sm:flex-row lg:flex-col"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.1 }}
              >
                <div className="flex h-14 w-14 items-center justify-center rounded-xl bg-blue-500 text-white sm:shrink-0">
                  <HiColorSwatch className="h-8 w-8" />
                </div>
                <div className="sm:min-w-0 sm:flex-1">
                  <h3 className="text-xl font-semibold leading-8 text-gray-900 mb-3">Professional Templates</h3>
                  <p className="text-base leading-7 text-gray-600">
                    Start with one of our professionally designed templates for various industries and formats.
                    Customize colors, text, and images to match your brand.
                  </p>
                </div>
              </motion.div>

              <motion.div
                className="flex flex-col gap-6 sm:flex-row lg:flex-col"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                <div className="flex h-14 w-14 items-center justify-center rounded-xl bg-blue-500 text-white sm:shrink-0">
                  <HiClock className="h-8 w-8" />
                </div>
                <div className="sm:min-w-0 sm:flex-1">
                  <h3 className="text-xl font-semibold leading-8 text-gray-900 mb-3">Simple Animation Tools</h3>
                  <p className="text-base leading-7 text-gray-600">
                    Add professional animations with just a few clicks. Our timeline-based animation system makes it
                    easy to create complex animations without any technical knowledge.
                  </p>
                </div>
              </motion.div>

              <motion.div
                className="flex flex-col gap-6 sm:flex-row lg:flex-col"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.3 }}
              >
                <div className="flex h-14 w-14 items-center justify-center rounded-xl bg-blue-500 text-white sm:shrink-0">
                  <HiDownload className="h-8 w-8" />
                </div>
                <div className="sm:min-w-0 sm:flex-1">
                  <h3 className="text-xl font-semibold leading-8 text-gray-900 mb-3">Multiple Export Options</h3>
                  <p className="text-base leading-7 text-gray-600">
                    Export your banners as HTML5, GIF, or video files. Our platform ensures your banners are compatible
                    with all major ad networks and platforms.
                  </p>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-white">
        <div className="mx-auto max-w-7xl px-6 lg:px-8 py-20 sm:py-28">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              Ready to create stunning banners?
            </h2>
            <p className="mt-6 text-lg leading-8 text-gray-600">
              Join thousands of marketers and designers who trust Banner.so for their creative needs.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Link href="/register">
                <Button
                  variant="outline"
                  className="rounded-full border-blue-500 text-blue-500 hover:bg-blue-50 hover:text-blue-600 px-6 py-3 text-base"
                >
                  Get started
                  <HiArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </Link>
              <Link href="/contact" className="text-base font-semibold leading-6 text-gray-900">
                Contact us <span aria-hidden="true">→</span>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </HomeLayout>
  );
}
