'use client';

import { HomeLayout } from '@/components/(marketing)/layout/HomeLayout';
import Link from 'next/link';
import { HiArrowLeft, HiCalendar, HiClock, HiUser, HiTag, HiShare } from 'react-icons/hi';
import { Button } from '@/components/ui/button';

// Safe content renderer for blog posts
const SafeContentRenderer = ({ content }: { content: string }) => {
  // Parse the content and render it safely
  const renderContent = () => {
    // Split by paragraphs and headers
    const parts = content.split(/(<h2>[\s\S]*?<\/h2>|<p>[\s\S]*?<\/p>|<ul>[\s\S]*?<\/ul>)/g);
    
    return parts.map((part, index) => {
      if (!part.trim()) return null;
      
      // Handle headers
      if (part.startsWith('<h2>') && part.endsWith('</h2>')) {
        const headerText = part.replace(/<\/?h2>/g, '');
        return (
          <h2 key={index} className="text-2xl font-bold text-gray-900 mt-8 mb-4">
            {headerText}
          </h2>
        );
      }
      
      // Handle paragraphs
      if (part.startsWith('<p>') && part.endsWith('</p>')) {
        const paragraphText = part.replace(/<\/?p>/g, '');
        return (
          <p key={index} className="text-gray-700 mb-4 leading-relaxed">
            {paragraphText}
          </p>
        );
      }
      
      // Handle lists
      if (part.startsWith('<ul>') && part.endsWith('</ul>')) {
        const listItems = part.match(/<li>(.*?)<\/li>/g) || [];
        return (
          <ul key={index} className="list-disc list-inside mb-4 space-y-2 text-gray-700">
            {listItems.map((item, itemIndex) => {
              const text = item.replace(/<\/?li>/g, '');
              return <li key={itemIndex}>{text}</li>;
            })}
          </ul>
        );
      }
      
      // Handle plain text
      if (part.trim() && !part.includes('<')) {
        return (
          <p key={index} className="text-gray-700 mb-4 leading-relaxed">
            {part.trim()}
          </p>
        );
      }
      
      return null;
    }).filter(Boolean);
  };

  return <div className="prose prose-blue prose-lg max-w-none">{renderContent()}</div>;
};

export function BlogPostPage({ post }) {
  // This is a placeholder component that would normally fetch the post data
  // based on the slug from a database or CMS

  // For demo purposes, we're using a sample post
  const samplePost = post || {
    id: 'html5-banner-best-practices',
    title: 'Best Practices for Creating Effective HTML5 Banners',
    excerpt: 'Learn the key principles and techniques for designing HTML5 banners that drive engagement and conversions.',
    coverImage: '/images/blog/html5-banner-best-practices.jpg',
    date: 'April 15, 2024',
    author: 'Sarah Johnson',
    authorRole: 'Senior Designer',
    readTime: '5 min read',
    category: 'Design Tips',
    tags: ['HTML5', 'Design', 'Best Practices'],
    content: `
      <p>HTML5 banners have become the standard for digital advertising, offering rich interactive experiences that static banners simply can't match. However, creating effective HTML5 banners requires more than just technical knowledge—it demands an understanding of design principles, user psychology, and performance optimization.</p>

      <h2>Keep File Sizes Small</h2>
      <p>One of the most important aspects of HTML5 banner creation is optimizing for file size. Large banners can slow down websites, leading to poor user experience and potentially higher bounce rates. Here are some tips for keeping your banner file sizes small:</p>
      <ul>
        <li>Compress all images using tools like TinyPNG or ImageOptim</li>
        <li>Use SVG for vector graphics whenever possible</li>
        <li>Minimize the use of JavaScript libraries</li>
        <li>Remove any unused CSS or JavaScript code</li>
      </ul>

      <h2>Design for Attention</h2>
      <p>You only have a few seconds to capture a user's attention. Make those seconds count with these design principles:</p>
      <ul>
        <li>Use contrasting colors to make your call-to-action stand out</li>
        <li>Keep your message clear and concise</li>
        <li>Use animation strategically to draw attention, not distract</li>
        <li>Include your brand logo prominently but not overwhelmingly</li>
      </ul>

      <h2>Optimize for Performance</h2>
      <p>Performance isn't just about file size—it's also about how efficiently your banner runs in the browser:</p>
      <ul>
        <li>Use CSS animations instead of JavaScript when possible</li>
        <li>Avoid complex animations that might cause lag on mobile devices</li>
        <li>Test your banners across different browsers and devices</li>
        <li>Consider using requestAnimationFrame for smoother animations</li>
      </ul>

      <h2>Include a Clear Call-to-Action</h2>
      <p>Every banner should have a purpose, and that purpose should be clear to the user:</p>
      <ul>
        <li>Use action-oriented language (e.g., "Shop Now," "Learn More," "Get Started")</li>
        <li>Make your CTA button large enough to be easily clickable</li>
        <li>Position your CTA where it will be seen (often at the end of the animation sequence)</li>
        <li>Use a color that stands out from the rest of the banner</li>
      </ul>

      <h2>Follow Ad Network Guidelines</h2>
      <p>Different ad networks have different requirements for HTML5 banners. Always check the specific guidelines for the platform you're targeting:</p>
      <ul>
        <li>Google Display Network has strict file size limits and content policies</li>
        <li>Some networks require specific clickTag implementations</li>
        <li>Be aware of animation duration limits (typically 15-30 seconds)</li>
        <li>Check if autoplay animations are allowed or if they require user interaction</li>
      </ul>

      <h2>Test, Measure, and Iterate</h2>
      <p>The most effective banner campaigns are those that continuously improve based on performance data:</p>
      <ul>
        <li>A/B test different designs, messages, and CTAs</li>
        <li>Track click-through rates and conversion rates</li>
        <li>Analyze heat maps to see where users are focusing their attention</li>
        <li>Use the insights gained to refine your future banner designs</li>
      </ul>

      <p>By following these best practices, you'll be well on your way to creating HTML5 banners that not only look great but also deliver results for your advertising campaigns.</p>
    `
  };

  return (
    <HomeLayout>
      <article className="bg-white">
        <div className="mx-auto max-w-3xl px-6 py-20">
          {/* Back to blog link */}
          <Link href="/blog" className="inline-flex items-center text-blue-600 hover:text-blue-700 mb-8">
            <HiArrowLeft className="h-4 w-4 mr-2" />
            Back to blog
          </Link>

          {/* Article header */}
          <header className="mb-12">
            <div className="flex items-center gap-4 text-sm text-gray-500 mb-4">
              <div className="flex items-center">
                <HiCalendar className="h-4 w-4 mr-1" />
                {samplePost.date}
              </div>
              <div className="flex items-center">
                <HiClock className="h-4 w-4 mr-1" />
                {samplePost.readTime}
              </div>
            </div>

            <h1 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              {samplePost.title}
            </h1>

            <div className="flex items-center gap-3 mb-6">
              <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 font-medium">
                {samplePost.author.charAt(0)}
              </div>
              <div>
                <p className="font-medium text-gray-900">{samplePost.author}</p>
                <p className="text-sm text-gray-500">{samplePost.authorRole}</p>
              </div>
            </div>

            <div className="flex flex-wrap gap-2 mb-8">
              {samplePost.tags.map((tag) => (
                <span key={tag} className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  <HiTag className="h-3 w-3 mr-1" />
                  {tag}
                </span>
              ))}
            </div>

            <div className="relative h-64 sm:h-80 w-full mb-8 overflow-hidden rounded-lg">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-blue-600 opacity-20"></div>
              <div className="absolute inset-0 flex items-center justify-center text-white text-xl font-medium">
                {samplePost.title}
              </div>
            </div>
          </header>

          {/* Article content */}
          <SafeContentRenderer content={samplePost.content} />

          {/* Share buttons */}
          <div className="mt-12 pt-6 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900">Share this article</h3>
              <div className="flex space-x-2">
                <Button variant="outline" size="sm" className="rounded-full">
                  <HiShare className="h-4 w-4 mr-2" />
                  Share
                </Button>
              </div>
            </div>
          </div>

          {/* Related posts would go here */}

          {/* Newsletter signup */}
          <div className="mt-16 p-6 bg-gray-50 rounded-lg">
            <h3 className="text-lg font-medium text-gray-900 mb-2">Subscribe to our newsletter</h3>
            <p className="text-gray-600 mb-4">
              Get the latest banner design tips and tutorials delivered to your inbox.
            </p>
            <div className="flex gap-2">
              <input
                type="email"
                placeholder="Enter your email"
                className="min-w-0 flex-auto rounded-md border-0 px-3.5 py-2 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"
              />
              <Button className="bg-blue-500 hover:bg-blue-600">
                Subscribe
              </Button>
            </div>
          </div>
        </div>
      </article>
    </HomeLayout>
  );
}
