'use client';

import Image from 'next/image';
import Link from 'next/link';

const navigation = {
  product: [
    { name: 'Features', href: '/#features' },
    { name: 'Examples', href: '/#examples' },
    { name: 'How It Works', href: '/#how-it-works' },
    { name: 'Pricing', href: '/#pricing' },
    // { name: 'Changelog', href: '/changelog' },
  ],

  company: [
    { name: 'About', href: '/about' },
    // { name: 'Blog', href: '/blog' },
    // { name: 'Support', href: '/support' },
    { name: 'Contact', href: '/contact' },
  ],
  legal: [
    { name: 'Privacy', href: '/privacy' },
    { name: 'Terms', href: '/terms' },
    { name: 'Cookie Policy', href: '/cookie-policy' },
  ],
  social: [
    {
      name: 'X',
      href: 'https://x.com/banner_so',
      icon: (props) => (
        <svg fill="currentColor" x="0px" y="0px" width="100" height="100" viewBox="0 0 50 50" {...props}>
          <path d="M 11 4 C 7.134 4 4 7.134 4 11 L 4 39 C 4 42.866 7.134 46 11 46 L 39 46 C 42.866 46 46 42.866 46 39 L 46 11 C 46 7.134 42.866 4 39 4 L 11 4 z M 13.085938 13 L 21.023438 13 L 26.660156 21.009766 L 33.5 13 L 36 13 L 27.789062 22.613281 L 37.914062 37 L 29.978516 37 L 23.4375 27.707031 L 15.5 37 L 13 37 L 22.308594 26.103516 L 13.085938 13 z M 16.914062 15 L 31.021484 35 L 34.085938 35 L 19.978516 15 L 16.914062 15 z"></path>
        </svg>
      ),
    },
    {
      name: 'LinkedIn',
      href: 'https://www.linkedin.com/company/banner-so',
      icon: (props) => (
        <svg fill="currentColor" x="0px" y="0px" width="100" height="100" viewBox="0 0 50 50" {...props}>
          <path d="M41,4H9C6.24,4,4,6.24,4,9v32c0,2.76,2.24,5,5,5h32c2.76,0,5-2.24,5-5V9C46,6.24,43.76,4,41,4z M17,20v19h-6V20H17z M11,14.47c0-1.4,1.2-2.47,3-2.47s2.93,1.07,3,2.47c0,1.4-1.12,2.53-3,2.53C12.2,17,11,15.87,11,14.47z M39,39h-6c0,0,0-9.26,0-10 c0-2-1-4-3.5-4.04h-0.08C27,24.96,26,27.02,26,29c0,0.91,0,10,0,10h-6V20h6v2.56c0,0,1.93-2.56,5.81-2.56 c3.97,0,7.19,2.73,7.19,8.26V39z"></path>
        </svg>
      ),
    },
  ],
};

export function Footer() {
  return (
    <footer className="bg-white border-t" aria-labelledby="footer-heading">
      <h2 id="footer-heading" className="sr-only">
        Footer
      </h2>
      <div className="mx-auto max-w-7xl px-6 pb-8 pt-16 sm:pt-24 lg:px-8">
        <div className="xl:grid xl:grid-cols-3 xl:gap-8">
          <div className="space-y-8">
            <Link href="/" className="flex items-center gap-2">
              <span className="bg-gradient-to-r from-blue-500 to-blue-600 text-white px-2 py-1 rounded-lg text-lg font-semibold shadow-sm">B</span>
              <span className="text-lg font-semibold text-gray-800">Banner.so</span>
            </Link>
            <p className="text-sm leading-6 text-gray-600">
            Design beautiful ad creatives in minutes with AI. <br/>Create animated HTML5 banners for your marketing campaigns.
            </p>
            <div className="flex space-x-6">
              {navigation.social.map((item) => (
                <a key={item.name} href={item.href} target="_blank" className="text-gray-400 hover:text-gray-500">
                  <span className="sr-only">{item.name}</span>
                  <item.icon className="h-6 w-6" aria-hidden="true" />
                </a>
              ))}

           </div>
          </div>
          <div className="mt-16 grid grid-cols-3 gap-8 xl:col-span-2 xl:mt-0">
            <div>
              <h3 className="text-sm font-semibold leading-6 text-gray-900">Product</h3>
              <ul role="list" className="mt-6 space-y-4">
                {navigation.product.map((item) => (
                  <li key={item.name}>
                    <a href={item.href} className="text-sm leading-6 text-gray-600 hover:text-gray-900">
                      {item.name}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
            <div>
              <h3 className="text-sm font-semibold leading-6 text-gray-900">Legal</h3>
              <ul role="list" className="mt-6 space-y-4">
                {navigation.legal.map((item) => (
                  <li key={item.name}>
                    <a href={item.href} className="text-sm leading-6 text-gray-600 hover:text-gray-900">
                      {item.name}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
            <div>
              <h3 className="text-sm font-semibold leading-6 text-gray-900">Company</h3>
              <ul role="list" className="mt-6 space-y-4">
                {navigation.company.map((item) => (
                  <li key={item.name}>
                    <a href={item.href} className="text-sm leading-6 text-gray-600 hover:text-gray-900">
                      {item.name}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
            <div className='flex flex-wrap gap-4 align-center justify-center mt-16 mb-0'>
               <a
                target="_blank"
                rel="noopener noreferrer"
                href="https://betalist.com/startups/banner?utm_campaign=badge-banner&utm_medium=badge&utm_source=badge-featured"
              >
                <img
                  alt="Banner.so - Create ads with AI in minutes | BetaList"
                  width={156}
                  height={54}
                  style={{ width: '156px', height: '54px' }}
                  src="https://betalist.com/badges/featured?id=124910&theme=color"
                />
              </a>

              <a href="https://startupfa.me/s/bannerso?utm_source=banner.so" target="_blank"><img src="https://startupfa.me/badges/featured-badge.webp" alt="Featured on Startup Fame" width="171" height="54" /></a>
              <a href="https://twelve.tools" target="_blank"><img src="https://twelve.tools/badge1-light.svg" alt="Featured on Twelve Tools" width="200" height="54"/></a>

           </div>
        <div className="mt-8 border-t border-gray-900/10 pt-8 text-center">
          <p className="text-xs leading-5 text-gray-500">&copy; {new Date().getFullYear()} Banner.so. Brought to you by
            <a href="https://x.com/serhataksakall/" target="_blank" className="text-blue-500 hover:underline">
              <Image
                src="https://pbs.twimg.com/profile_images/1747867467035152384/eJ2FapQR_400x400.jpg"
                alt="Serhat Aksakal"
                width={20}
                height={20}
                className="inline-block rounded-full mx-1"
              />
              Serhat
            </a>.
          </p>
        </div>
      </div>
    </footer>
  );
}