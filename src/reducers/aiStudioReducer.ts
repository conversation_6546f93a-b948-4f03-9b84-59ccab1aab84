'use client';

/**
 * AI Studio Reducer
 * Manages state for the AI Studio panel
 */

// Initial state
export const initialState = {
  topic: '',
  selectedFormat: '',
  imageTemplate: 'model_closeup_emotive_text_overlay', // Default to our new text overlay template
  isGenerating: false,
  error: null,
  generations: [],
  isLoading: false,
  generationProgress: null
};

// Action types
export const ACTION_TYPES = {
  SET_TOPIC: 'SET_TOPIC',
  SET_SELECTED_FORMAT: 'SET_SELECTED_FORMAT',
  SET_IMAGE_TEMPLATE: 'SET_IMAGE_TEMPLATE',
  SET_IS_GENERATING: 'SET_IS_GENERATING',
  SET_ERROR: 'SET_ERROR',
  CLEAR_ERROR: 'CLEAR_ERROR',
  SET_GENERATIONS: 'SET_GENERATIONS',
  ADD_GENERATION: 'ADD_GENERATION',
  SET_IS_LOADING: 'SET_IS_LOADING',
  UPDATE_GENERATION_PROGRESS: 'UPDATE_GENERATION_PROGRESS'
};

// Reducer function
export const aiStudioReducer = (state, action) => {
  switch (action.type) {
    case ACTION_TYPES.SET_TOPIC:
      return {
        ...state,
        topic: action.payload,
        error: null // Clear error when topic changes
      };

    case ACTION_TYPES.SET_SELECTED_FORMAT:
      return {
        ...state,
        selectedFormat: action.payload
      };

    case ACTION_TYPES.SET_IMAGE_TEMPLATE:
      return {
        ...state,
        imageTemplate: action.payload
      };

    case ACTION_TYPES.SET_IS_GENERATING:
      return {
        ...state,
        isGenerating: action.payload
      };

    case ACTION_TYPES.SET_ERROR:
      return {
        ...state,
        error: action.payload,
        isGenerating: false // Stop generating on error
      };

    case ACTION_TYPES.CLEAR_ERROR:
      return {
        ...state,
        error: null
      };

    case ACTION_TYPES.SET_GENERATIONS:
      return {
        ...state,
        generations: action.payload,
        isLoading: false
      };

    case ACTION_TYPES.ADD_GENERATION:
      return {
        ...state,
        generations: [action.payload, ...state.generations],
        topic: '', // Clear topic after successful generation
        isGenerating: false
      };

    case ACTION_TYPES.SET_IS_LOADING:
      return {
        ...state,
        isLoading: action.payload
      };

    case ACTION_TYPES.UPDATE_GENERATION_PROGRESS:
      return {
        ...state,
        generationProgress: {
          ...state.generationProgress,
          ...action.payload
        }
      };

    // Prompt template cases have been removed

    default:
      return state;
  }
};
