import { NextRequest, NextResponse } from 'next/server';
import { NextApiRequest, NextApiResponse } from 'next';

// Next.js API route types
export type NextApiHandler<T = any> = (
  req: NextApiRequest,
  res: NextApiResponse<T>
) => void | Promise<void>;

// Next.js App Router route handler types
export type RouteHandler = (
  request: NextRequest,
  context: { params: Record<string, string | string[]> }
) => Promise<NextResponse> | NextResponse;

// Next.js page props types
export interface PageProps {
  params?: Record<string, string | string[]>;
  searchParams?: Record<string, string | string[]>;
}

// Next.js layout props types
export interface LayoutProps {
  children: React.ReactNode;
  params?: Record<string, string | string[]>;
}
