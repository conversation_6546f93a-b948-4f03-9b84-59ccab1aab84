import { AppRole } from '../database.types';

// Basic user form data interface
export interface UserFormData {
  email: string;
  firstName?: string | null;
  lastName?: string | null;
  subscriptionTier?: string;
}

// User creation form data
export interface UserCreateFormData extends UserFormData {
  password: string;
  role?: AppRole;
}

// User update form data
export interface UserUpdateFormData extends Partial<UserFormData> {
  id: string;
  role?: AppRole;
  password?: string;
}

// User profile form data
export interface UserProfileFormData {
  firstName: string;
  lastName: string;
  email: string;
  currentPassword?: string;
  newPassword?: string;
  confirmPassword?: string;
}

// User response data from API
export interface UserResponseData {
  id: string;
  email: string;
  firstName: string | null;
  lastName: string | null;
  avatarUrl: string | null;
  subscriptionTier: string;
  role: AppRole;
  createdAt: string;
  updatedAt: string | null;
}

// Password reset form data
export interface PasswordResetFormData {
  email: string;
}

// Password update form data
export interface PasswordUpdateFormData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}
