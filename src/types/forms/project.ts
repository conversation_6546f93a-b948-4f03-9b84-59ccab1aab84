// Basic project form data interface
export interface ProjectFormData {
  id?: string;
  userId?: string;
  title: string;
  description?: string | null;
  status?: string;
  createdAt?: string;
  updatedAt?: string;
}

// Project creation form data
export interface ProjectCreateFormData extends ProjectFormData {
  // Additional fields specific to creation
}

// Project update form data
export interface ProjectUpdateFormData {
  title?: string;
  description?: string | null;
  status?: string;
}

// Project response data from API
export interface ProjectResponseData {
  id: string;
  user_id: string;
  title: string;
  description: string | null;
  status: string;
  created_at: string;
  updated_at: string;
  banners_count?: number;
}

// Mapped project data (from snake_case to camelCase)
export interface MappedProjectData {
  id: string;
  userId: string;
  title: string;
  description: string | null;
  status: string;
  createdAt: string;
  updatedAt: string;
  bannersCount?: number;
}
