import { Banner, User, Format, AIGeneration, Subscription, Project } from './index';

// API request types
export interface ApiRequest {
  method: string;
  url: string;
  data?: any;
  headers?: Record<string, string>;
}

// API response types
export interface ApiResponse<T> {
  data?: T;
  error?: string;
  message?: string;
  status?: number;
}

// Banner API types
export interface CreateBannerRequest {
  title: string;
  format_key: string;
  project_id?: string;
}

export interface UpdateBannerRequest {
  title?: string;
  elements?: any[];
  settings?: any;
  format_key?: string;
  project_id?: string;
}

export interface BannerResponse extends ApiResponse<Banner> {}
export interface BannersResponse extends ApiResponse<Banner[]> {}

// Project API types
export interface CreateProjectRequest {
  name: string;
  description?: string;
}

export interface UpdateProjectRequest {
  name?: string;
  description?: string;
}

export interface ProjectResponse extends ApiResponse<Project> {}
export interface ProjectsResponse extends ApiResponse<Project[]> {}

// User API types
export interface UpdateUserRequest {
  full_name?: string;
  avatar_url?: string;
}

export interface UserResponse extends ApiResponse<User> {}

// Format API types
export interface FormatResponse extends ApiResponse<Format> {}
export interface FormatsResponse extends ApiResponse<Format[]> {}

// AI Generation API types
export interface CreateAIGenerationRequest {
  prompt: string;
  banner_id?: string;
}

export interface AIGenerationResponse extends ApiResponse<AIGeneration> {}
export interface AIGenerationsResponse extends ApiResponse<AIGeneration[]> {}

// Subscription API types
export interface CreateCheckoutRequest {
  plan_id: string;
  success_url: string;
  cancel_url: string;
}

export interface SubscriptionResponse extends ApiResponse<Subscription> {}

// Auth API types
export interface SignInRequest {
  email: string;
  password: string;
}

export interface SignUpRequest {
  email: string;
  password: string;
  full_name: string;
}

export interface ResetPasswordRequest {
  email: string;
}

export interface UpdatePasswordRequest {
  password: string;
}
