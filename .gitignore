# Dependencies
/node_modules
/.pnp
.pnp.js
*.db
# Testing
/coverage
.DS_Store

# Next.js
/.next/
/out/
/public/uploads/*
/public/banners/*
# Production
/build

# Misc
.DS_Store
*.pem

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Local env files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Vercel
.vercel

# Uploads
/public/uploads/*
!/public/uploads/.gitkeep

/landing-page-example-for-documentation-purposes/*

.next/*
node_modules/*
*.sql

# Added by <PERSON> Task Master
# Logs
logs
*.log
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific
# Task files
tasks.json
tasks/

public/uploads/*
public/banners/*
public/exports/*

*.tsbuildinfo