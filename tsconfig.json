{"compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": false, "noEmit": true, "incremental": true, "module": "esnext", "esModuleInterop": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "baseUrl": ".", "paths": {"@/*": ["src/*"], "@components/*": ["src/components/*"], "@constants/*": ["src/constants/*"], "@pages/*": ["src/old-pages/*"], "@utils/*": ["src/utils/*"], "@hooks/*": ["src/hooks/*"], "@styles/*": ["src/styles/*"], "@lib/*": ["src/lib/*"], "@contexts/*": ["src/contexts/*"]}, "plugins": [{"name": "next"}]}, "include": ["next-env.d.ts", ".next/types/**/*.ts", "**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx"], "exclude": ["node_modules"]}